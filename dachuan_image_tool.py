import os
import sys
import json
import logging
import shutil
import tempfile
import gc  # 垃圾回收
import threading
import time
from concurrent.futures import ThreadPoolExecutor
from typing import List
from PyQt6.QtWidgets import (
    QApplication, QWidget, QPushButton, QVBoxLayout,
    QFileDialog, QMessageBox, QLabel, QMainWindow,
    QProgressBar, QTextEdit, QGroupBox, QHBoxLayout,
    QLineEdit, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QUrl
from PyQt6.QtGui import QTextCursor, QDragEnterEvent, QDropEvent

# 导入主项目的登录模块
from ui.login_dialog import LoginDialog
from utils.supabase_helper import SupabaseHelper
from PIL import Image  # 用于图片处理
import warnings

# 导入自定义模块
try:
    from core.image_indexer_duckdb import ImageIndexerDuckDB
    HAS_IMAGE_INDEXER = True
except ImportError:
    HAS_IMAGE_INDEXER = False
    logging.warning("图库索引器模块未找到，索引功能将被禁用")

# 尝试导入拼音库，如果没有则使用内置映射
try:
    from pypinyin import lazy_pinyin, Style
    HAS_PYPINYIN = True
except ImportError:
    HAS_PYPINYIN = False

# 尝试导入psutil，如果没有则使用替代方案
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

# 使用统一的PIL配置管理
try:
    from utils.pil_config import PILConfigManager
    # 为图片工具配置PIL（使用较大的限制以处理大图片）
    PILConfigManager.configure_pil(
        max_image_pixels=1_500_000_000,  # 1.5G像素
        load_truncated_images=True,
        ignore_decompression_warning=True,
        force_reconfigure=True  # 强制重新配置以确保使用正确的参数
    )
except ImportError:
    # 如果统一配置模块不可用，使用原来的配置方式
    Image.MAX_IMAGE_PIXELS = 1_500_000_000
    warnings.filterwarnings("ignore", category=Image.DecompressionBombWarning)

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()  # 输出到控制台
    ]
)
log = logging.getLogger(__name__)

# 使用主项目的Supabase配置
# 通过SupabaseHelper统一管理

# 应用名称 & 当前应用版本
ROBOT_SMART_NAME = "DeAI-图库管理工具"
ROBOT_CURRENT_VERSION = "0.0.6"

IMAGE_FINDER_BOT_TAG = 'dachuan_robot_001'

# 宽高比比较容差
ASPECT_RATIO_TOLERANCE = 0.01

# 支持的图片格式
SUPPORTED_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp'}

# 处理相关常量
DEFAULT_BATCH_SIZE = 100
LARGE_IMAGE_PIXEL_THRESHOLD = 50_000_000  # 5000万像素
MAX_FILENAME_COUNTER = 100  # 文件名计数器最大值
MEMORY_CHECK_INTERVAL = 1000  # 内存检查间隔（文件数）

# 用户会话由SupabaseHelper统一管理

# 全局变量，用于存储主窗口实例的引用
main_window_instance = None

# 设置主窗口实例的引用
def set_main_window_instance(instance):
    global main_window_instance
    main_window_instance = instance

# 路径规范化辅助函数
def normalize_path_for_display(path):
    """规范化路径用于显示，统一使用正斜杠"""
    if not path:
        return path
    try:
        # 统一使用正斜杠，处理Windows路径
        path = str(path).replace('\\', '/')
        # 处理连续的斜杠
        while '//' in path:
            path = path.replace('//', '/')
        # 移除路径首尾的空白字符
        path = path.strip()
        # 移除路径末尾的斜杠（除非是根路径）
        if path.endswith('/') and len(path) > 1:
            path = path.rstrip('/')
        return path
    except Exception:
        return str(path) if path else ""

# 网络请求日志记录函数
def log_network_request(message):
    """记录网络请求日志"""
    logging.info(f"网络请求: {message}")
    # 如果主窗口实例存在，也在UI中显示日志
    if main_window_instance:
        try:
            main_window_instance.update_log(message)
        except Exception as e:
            logging.error(f"更新UI日志时出错: {e}")

# 使用主项目的SupabaseHelper统一管理认证客户端

# 使用主项目的LoginDialog类


# ---------------------------
# 中文拼音处理辅助函数
# ---------------------------
def get_chinese_first_letter(chinese_char):
    """
    获取中文字符的拼音首字母
    如果有pypinyin库则使用，否则使用内置映射表
    """
    if HAS_PYPINYIN:
        try:
            pinyin_list = lazy_pinyin(chinese_char, style=Style.FIRST_LETTER)
            if pinyin_list:
                return pinyin_list[0].upper()
        except Exception:
            pass
    
    # 内置常用汉字拼音首字母映射表（简化版）
    chinese_pinyin_map = {
        # A
        '阿': 'A', '啊': 'A', '爱': 'A', '安': 'A', '按': 'A', '暗': 'A',
        # B
        '八': 'B', '把': 'B', '白': 'B', '百': 'B', '班': 'B', '半': 'B', '帮': 'B', '包': 'B', '宝': 'B', '报': 'B', '被': 'B', '本': 'B', '比': 'B', '别': 'B', '不': 'B',
        # C
        '才': 'C', '菜': 'C', '草': 'C', '茶': 'C', '长': 'C', '常': 'C', '车': 'C', '成': 'C', '吃': 'C', '出': 'C', '穿': 'C', '床': 'C', '春': 'C', '次': 'C', '从': 'C', '错': 'C',
        # D
        '大': 'D', '带': 'D', '单': 'D', '当': 'D', '到': 'D', '得': 'D', '的': 'D', '地': 'D', '第': 'D', '点': 'D', '电': 'D', '店': 'D', '东': 'D', '都': 'D', '读': 'D', '对': 'D', '多': 'D',
        # E
        '儿': 'E', '二': 'E',
        # F
        '发': 'F', '法': 'F', '饭': 'F', '方': 'F', '房': 'F', '放': 'F', '飞': 'F', '分': 'F', '风': 'F', '服': 'F', '父': 'F', '妇': 'F', '复': 'F',
        # G
        '该': 'G', '干': 'G', '感': 'G', '刚': 'G', '高': 'G', '告': 'G', '哥': 'G', '给': 'G', '跟': 'G', '更': 'G', '工': 'G', '公': 'G', '狗': 'G', '够': 'G', '姑': 'G', '关': 'G', '管': 'G', '光': 'G', '广': 'G', '国': 'G', '过': 'G',
        # H
        '还': 'H', '孩': 'H', '海': 'H', '汉': 'H', '好': 'H', '号': 'H', '喝': 'H', '和': 'H', '很': 'H', '后': 'H', '候': 'H', '花': 'H', '话': 'H', '画': 'H', '坏': 'H', '欢': 'H', '换': 'H', '黄': 'H', '回': 'H', '会': 'H', '火': 'H', '或': 'H',
        # J
        '机': 'J', '鸡': 'J', '几': 'J', '家': 'J', '加': 'J', '间': 'J', '见': 'J', '叫': 'J', '教': 'J', '接': 'J', '街': 'J', '结': 'J', '姐': 'J', '介': 'J', '今': 'J', '进': 'J', '近': 'J', '就': 'J', '九': 'J', '酒': 'J', '觉': 'J',
        # K
        '开': 'K', '看': 'K', '考': 'K', '可': 'K', '课': 'K', '空': 'K', '口': 'K', '哭': 'K', '快': 'K', '块': 'K',
        # L
        '来': 'L', '老': 'L', '了': 'L', '累': 'L', '冷': 'L', '离': 'L', '里': 'L', '礼': 'L', '理': 'L', '立': 'L', '脸': 'L', '两': 'L', '亮': 'L', '了': 'L', '六': 'L', '楼': 'L', '路': 'L', '绿': 'L',
        # M
        '妈': 'M', '马': 'M', '买': 'M', '卖': 'M', '满': 'M', '慢': 'M', '忙': 'M', '猫': 'M', '没': 'M', '美': 'M', '每': 'M', '门': 'M', '们': 'M', '米': 'M', '面': 'M', '民': 'M', '明': 'M', '名': 'M', '母': 'M',
        # N
        '那': 'N', '哪': 'N', '奶': 'N', '男': 'N', '南': 'N', '难': 'N', '呢': 'N', '能': 'N', '你': 'N', '年': 'N', '鸟': 'N', '牛': 'N', '女': 'N',
        # O
        '哦': 'O',
        # P
        '爬': 'P', '怕': 'P', '拍': 'P', '跑': 'P', '朋': 'P', '皮': 'P', '片': 'P', '漂': 'P', '票': 'P', '苹': 'P', '平': 'P', '婆': 'P',
        # Q
        '七': 'Q', '妻': 'Q', '其': 'Q', '起': 'Q', '气': 'Q', '汽': 'Q', '前': 'Q', '钱': 'Q', '亲': 'Q', '青': 'Q', '轻': 'Q', '清': 'Q', '情': 'Q', '请': 'Q', '去': 'Q', '全': 'Q', '群': 'Q',
        # R
        '然': 'R', '让': 'R', '人': 'R', '认': 'R', '日': 'R', '容': 'R', '如': 'R', '入': 'R',
        # S
        '三': 'S', '色': 'S', '山': 'S', '上': 'S', '少': 'S', '谁': 'S', '什': 'S', '身': 'S', '深': 'S', '生': 'S', '声': 'S', '时': 'S', '十': 'S', '是': 'S', '事': 'S', '手': 'S', '书': 'S', '水': 'S', '睡': 'S', '说': 'S', '四': 'S', '送': 'S', '苏': 'S', '算': 'S', '岁': 'S',
        # T
        '他': 'T', '她': 'T', '它': 'T', '太': 'T', '天': 'T', '田': 'T', '条': 'T', '跳': 'T', '听': 'T', '同': 'T', '头': 'T', '图': 'T', '土': 'T',
        # W
        '外': 'W', '完': 'W', '玩': 'W', '晚': 'W', '万': 'W', '王': 'W', '往': 'W', '忘': 'W', '为': 'W', '位': 'W', '文': 'W', '问': 'W', '我': 'W', '五': 'W', '午': 'W', '舞': 'W', '物': 'W',
        # X
        '西': 'X', '希': 'X', '洗': 'X', '喜': 'X', '下': 'X', '夏': 'X', '先': 'X', '现': 'X', '想': 'X', '向': 'X', '像': 'X', '小': 'X', '笑': 'X', '些': 'X', '写': 'X', '谢': 'X', '新': 'X', '心': 'X', '信': 'X', '星': 'X', '行': 'X', '姓': 'X', '兄': 'X', '休': 'X', '学': 'X', '雪': 'X',
        # Y
        '呀': 'Y', '眼': 'Y', '颜': 'Y', '羊': 'Y', '样': 'Y', '要': 'Y', '也': 'Y', '爷': 'Y', '叶': 'Y', '一': 'Y', '衣': 'Y', '医': 'Y', '已': 'Y', '以': 'Y', '意': 'Y', '因': 'Y', '音': 'Y', '银': 'Y', '应': 'Y', '英': 'Y', '用': 'Y', '友': 'Y', '有': 'Y', '又': 'Y', '右': 'Y', '鱼': 'Y', '雨': 'Y', '语': 'Y', '玉': 'Y', '元': 'Y', '园': 'Y', '远': 'Y', '月': 'Y', '越': 'Y', '云': 'Y', '运': 'Y',
        # Z
        '在': 'Z', '再': 'Z', '早': 'Z', '怎': 'Z', '站': 'Z', '张': 'Z', '长': 'Z', '找': 'Z', '照': 'Z', '这': 'Z', '真': 'Z', '正': 'Z', '知': 'Z', '只': 'Z', '中': 'Z', '种': 'Z', '重': 'Z', '住': 'Z', '主': 'Z', '注': 'Z', '祝': 'Z', '准': 'Z', '桌': 'Z', '字': 'Z', '自': 'Z', '走': 'Z', '最': 'Z', '左': 'Z', '作': 'Z', '做': 'Z', '坐': 'Z'
    }
    
    # 查找映射表
    if chinese_char in chinese_pinyin_map:
        return chinese_pinyin_map[chinese_char]
    
    # 如果找不到，根据Unicode范围大致判断
    char_code = ord(chinese_char)
    if 0x4e00 <= char_code <= 0x9fff:  # 中文字符范围
        # 简单的Unicode范围映射（不够精确，但可以作为备用）
        ranges = [
            (0x4e00, 0x4fff, 'A'), (0x5000, 0x50ff, 'B'), (0x5100, 0x51ff, 'C'),
            (0x5200, 0x52ff, 'D'), (0x5300, 0x53ff, 'E'), (0x5400, 0x54ff, 'F'),
            (0x5500, 0x55ff, 'G'), (0x5600, 0x56ff, 'H'), (0x5700, 0x57ff, 'J'),
            (0x5800, 0x58ff, 'K'), (0x5900, 0x59ff, 'L'), (0x5a00, 0x5aff, 'M'),
            (0x5b00, 0x5bff, 'N'), (0x5c00, 0x5cff, 'P'), (0x5d00, 0x5dff, 'Q'),
            (0x5e00, 0x5eff, 'R'), (0x5f00, 0x5fff, 'S'), (0x6000, 0x60ff, 'T'),
            (0x6100, 0x61ff, 'W'), (0x6200, 0x62ff, 'X'), (0x6300, 0x63ff, 'Y'),
            (0x6400, 0x9fff, 'Z')
        ]
        
        for start, end, letter in ranges:
            if start <= char_code <= end:
                return letter
    
    # 默认返回Z
    return 'Z'

def get_folder_name_for_chinese_filename(filename):
    """
    根据中文文件名获取应该归类的文件夹名
    规则：
    1. 如果不是中文开头，归到 '1字母数字' 文件夹
    2. 如果是中文开头，归到第一个中文字符的拼音首字母文件夹
    """
    if not filename:
        return '1字母数字'
    
    import re
    
    # 分离文件名和扩展名
    name, _ = os.path.splitext(filename)
    
    # 检查第一个字符
    if name:
        first_char = name[0]
        
        # 检查是否为中文字符
        if re.match(r'[\u4e00-\u9fff]', first_char):
            # 中文开头，获取拼音首字母
            pinyin_letter = get_chinese_first_letter(first_char)
            return pinyin_letter.upper()
        else:
            # 非中文开头（字母、数字、符号等），归到 '1字母数字' 文件夹
            return '1字母数字'
    
    return '1字母数字'


# ---------------------------
# 系统资源监控器
# ---------------------------
class SystemResourceMonitor:
    """系统资源监控器 - 增强版本"""

    @staticmethod
    def get_memory_usage():
        """获取当前内存使用情况"""
        if HAS_PSUTIL:
            try:
                memory = psutil.virtual_memory()
                return {
                    'total': memory.total,
                    'available': memory.available,
                    'percent': memory.percent,
                    'used': memory.used
                }
            except Exception:
                pass

        # 如果没有psutil或出错，使用简单的gc统计
        return {
            'objects': len(gc.get_objects()),
            'collections': gc.get_count()
        }

    @staticmethod
    def is_memory_pressure():
        """检查是否存在内存压力"""
        if HAS_PSUTIL:
            try:
                memory = psutil.virtual_memory()
                return memory.percent > 85  # 内存使用超过85%
            except Exception:
                pass
        return False

    @staticmethod
    def get_cpu_count():
        """获取CPU核心数"""
        try:
            return os.cpu_count() or 4
        except Exception:
            return 4


# ---------------------------
# 多线程任务管理器
# ---------------------------
class TaskManager:
    """多线程任务管理器"""

    def __init__(self, max_workers=None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.executor = None
        self.futures = []
        self._stop_event = threading.Event()

    def __enter__(self):
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()

    def submit_task(self, func, *args, **kwargs):
        """提交任务"""
        if self.executor and not self._stop_event.is_set():
            future = self.executor.submit(func, *args, **kwargs)
            self.futures.append(future)
            return future
        return None

    def stop(self):
        """停止所有任务"""
        self._stop_event.set()

    def is_stopped(self):
        """检查是否已停止"""
        return self._stop_event.is_set()

    def shutdown(self, wait=True):
        """关闭执行器"""
        if self.executor:
            self.executor.shutdown(wait=wait)
            self.executor = None
        self.futures.clear()

    def get_completed_tasks(self):
        """获取已完成的任务"""
        completed = []
        remaining = []

        for future in self.futures:
            if future.done():
                completed.append(future)
            else:
                remaining.append(future)

        self.futures = remaining
        return completed

# ---------------------------
# 超高性能多线程文件名清洗器
# ---------------------------
class UltraHighPerformanceImageNameCleaner:
    """超高性能多线程图片文件名清洗器 - 彻底解决崩溃问题"""

    def __init__(self, max_workers=None, enable_duplicate_numbering=False, clean_type='数字'):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(8, SystemResourceMonitor.get_cpu_count())
        self._task_manager = None
        self._enable_duplicate_numbering = enable_duplicate_numbering  # 同名图片序号开关，默认关闭
        self._duplicate_counter = {}  # 用于跟踪同名文件的计数器
        self._sorted_files_map = {}  # 用于存储按宽高比排序的同名文件映射
        self._clean_type = clean_type  # 清洗类型：'数字'、'字母'、'中文'



    @staticmethod
    def clean_filename_by_type(filename, clean_type='数字'):
        """
        根据指定类型清洗文件名
        clean_type: '数字' (默认), '字母', '中文'
        """
        if not filename:
            return filename

        # 分离文件名和扩展名
        name, ext = os.path.splitext(filename)
        import re

        if clean_type == '数字':
            return UltraHighPerformanceImageNameCleaner._clean_digital_type(name, ext)
        elif clean_type == '字母':
            return UltraHighPerformanceImageNameCleaner._clean_letter_type(name, ext)
        elif clean_type == '中文':
            return UltraHighPerformanceImageNameCleaner._clean_chinese_type(name, ext)
        else:
            # 默认使用数字类型清洗
            return UltraHighPerformanceImageNameCleaner._clean_digital_type(name, ext)

    @staticmethod
    def _clean_digital_type(name, ext):
        """
        数字类型清洗：保留字母前缀和数字部分，移除其他字符
        例如：'O0027水蜜桃.jpg' -> 'O0027.jpg'
        """
        import re
        
        # 首先尝试匹配开头的字母（可选）+ 紧接着的数字
        match = re.match(r'^([A-Za-z]*\d+)', name)
        if match:
            return match.group(1) + ext

        # 尝试在整个文件名中找到字母+数字的连续组合
        # 匹配字母紧接着数字的模式
        letter_number_match = re.search(r'([A-Za-z]+\d+)', name)
        if letter_number_match:
            return letter_number_match.group(1) + ext

        # 尝试找到分离的字母和数字，并组合它们
        # 先找所有的字母序列
        letter_matches = re.findall(r'[A-Za-z]+', name)
        # 再找所有的数字序列
        number_matches = re.findall(r'\d+', name)

        if letter_matches and number_matches:
            # 取第一个字母序列和第一个数字序列
            return letter_matches[0] + number_matches[0] + ext

        # 如果没有找到字母+数字的组合，尝试只提取第一组数字
        if number_matches:
            return number_matches[0] + ext

        # 如果没有找到任何数字，返回原文件名
        return name + ext

    @staticmethod
    def _clean_letter_type(name, ext):
        """
        字母类型清洗：保留纯字母或字母开头的字母数字组合
        例如：'Photo123中文.jpg' -> 'Photo123.jpg'
        例如：'ABC测试456.jpg' -> 'ABC456.jpg'
        例如：'美女Photo.jpg' -> 'Photo.jpg'
        """
        import re
        
        # 优先匹配字母开头的字母数字组合
        letter_digit_match = re.match(r'^([A-Za-z]+(?:\d+)?)', name)
        if letter_digit_match:
            result = letter_digit_match.group(1)
            # 如果结果只是纯字母，尝试添加后面的数字
            if not re.search(r'\d', result):
                # 查找后续的数字
                remaining = name[len(result):]
                digit_match = re.search(r'(\d+)', remaining)
                if digit_match:
                    result += digit_match.group(1)
            return result + ext
        
        # 如果开头不是字母，尝试找到文件名中的字母序列
        letter_matches = re.findall(r'[A-Za-z]+', name)
        if letter_matches:
            # 取第一个字母序列
            main_letters = letter_matches[0]
            
            # 查找数字序列
            digit_matches = re.findall(r'\d+', name)
            if digit_matches:
                # 添加第一个数字序列
                return main_letters + digit_matches[0] + ext
            else:
                # 纯字母
                return main_letters + ext
        
        # 如果没有找到字母，返回原文件名
        return name + ext

    @staticmethod
    def _clean_chinese_type(name, ext):
        """
        中文类型清洗：保留中文字符和特殊符号，只移除数字
        例如：'美女123.jpg' -> '美女.jpg'
        例如：'【粉】卷毛小狗梦幻星123.jpg' -> '【粉】卷毛小狗梦幻星.jpg'
        例如：'Photo美女456.jpg' -> 'Photo美女.jpg'
        """
        import re
        
        # 检查是否包含中文字符
        has_chinese = bool(re.search(r'[\u4e00-\u9fff]', name))
        
        if has_chinese:
            # 包含中文字符，只移除数字，保留中文、字母、特殊符号
            result = re.sub(r'\d+', '', name)
            # 清理可能产生的多余空格
            result = re.sub(r'\s+', '', result)
            return result + ext
        else:
            # 不包含中文字符，回退到字母处理
            letter_matches = re.findall(r'[A-Za-z]+', name)
            if letter_matches:
                return letter_matches[0] + ext
            
            # 如果既没有中文也没有字母，返回原文件名
            return name + ext

    @staticmethod
    def clean_filename(filename, clean_type='数字'):
        """
        清洗文件名的主入口方法，支持多种清洗类型
        clean_type: '数字' (默认), '字母', '中文'
        """
        if not filename:
            return filename

        return UltraHighPerformanceImageNameCleaner.clean_filename_by_type(filename, clean_type)


    def generate_unique_filename(self, base_path, filename, original_filename=None):
        """
        生成唯一的文件名，避免冲突
        超高性能版本：批量检查存在性
        支持基于宽高比排序的同名图片序号功能
        """
        name, ext = os.path.splitext(filename)
        target_path = os.path.join(base_path, filename)

        # 如果启用了同名图片序号功能且有排序信息
        if self._enable_duplicate_numbering and original_filename:
            with self._lock:
                # 使用清洗后的文件名（不含扩展名）作为key
                base_name = name

                # 检查是否有预排序的文件列表
                if base_name in self._sorted_files_map:
                    sorted_files = self._sorted_files_map[base_name]
                    # 找到当前原始文件在排序列表中的位置
                    try:
                        index = sorted_files.index(original_filename)
                        if index == 0:
                            # 宽高比最大的文件，保持原清洗后的文件名
                            return filename
                        else:
                            # 其他文件添加序号
                            return f"{base_name}_{index + 1}{ext}"
                    except ValueError:
                        # 如果找不到文件，使用备用逻辑
                        pass

                # 备用逻辑：使用原来的计数器方式
                if base_name in self._duplicate_counter:
                    self._duplicate_counter[base_name] += 1
                    counter = self._duplicate_counter[base_name]
                    new_filename = f"{base_name}_{counter}{ext}"
                    return new_filename
                else:
                    self._duplicate_counter[base_name] = 1
                    return filename

        # 如果没有启用同名图片序号功能，使用原来的逻辑
        # 如果文件不存在，直接返回原文件名
        if not os.path.exists(target_path):
            return filename

        # 批量生成候选文件名并检查
        for counter in range(1, 101):  # 限制在100次内
            new_filename = f"{name}_{counter}{ext}"
            new_path = os.path.join(base_path, new_filename)
            if not os.path.exists(new_path):
                return new_filename

        # 如果100次都失败，使用时间戳和随机数
        import random
        timestamp = int(time.time() * 1000) % 100000
        random_num = random.randint(1000, 9999)
        return f"{name}_{timestamp}_{random_num}{ext}"

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True
            if self._task_manager:
                self._task_manager.stop()

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    def _process_file_batch(self, batch_info):
        """
        处理单个文件批次 - 多线程工作函数
        """
        batch_files, folder_path, batch_num = batch_info

        results = {
            'batch_num': batch_num,
            'processed': 0,
            'renamed': 0,
            'errors': 0,
            'conflicts': 0,
            'logs': []
        }

        try:
            for original_filename, clean_filename in batch_files:
                if self.is_stop_requested():
                    break

                try:
                    old_path = os.path.join(folder_path, original_filename)

                    # 检查文件是否存在
                    if not os.path.exists(old_path):
                        results['logs'].append(f"⚠️ 跳过: {original_filename} (文件不存在)")
                        continue

                    # 中文类型特殊处理：根据文件名确定子文件夹
                    final_folder_path = folder_path
                    final_clean_filename = clean_filename
                    
                    if self._clean_type == '中文':
                        # 获取应该归类的文件夹名
                        folder_name = get_folder_name_for_chinese_filename(clean_filename)
                        final_folder_path = os.path.join(folder_path, folder_name)
                        
                        # 确保子文件夹存在
                        if not os.path.exists(final_folder_path):
                            try:
                                os.makedirs(final_folder_path, exist_ok=True)
                                results['logs'].append(f"📁 创建分类文件夹: {folder_name}")
                            except Exception as e:
                                results['errors'] += 1
                                results['logs'].append(f"❌ 无法创建分类文件夹 {final_folder_path}: {str(e)}")
                                continue
                        
                        results['logs'].append(f"📂 中文分类: {clean_filename} → {folder_name} 文件夹")

                    # 生成唯一文件名
                    unique_filename = self.generate_unique_filename(final_folder_path, final_clean_filename, original_filename)
                    new_path = os.path.join(final_folder_path, unique_filename)

                    # 执行重命名/移动
                    success = self._safe_rename_with_retry(old_path, new_path, original_filename)

                    if success:
                        results['renamed'] += 1
                        if unique_filename != final_clean_filename:
                            results['conflicts'] += 1
                            if self._clean_type == '中文':
                                results['logs'].append(f"✅ 重命名并分类(解决冲突): {original_filename} → {folder_name}/{unique_filename}")
                            else:
                                results['logs'].append(f"✅ 重命名(解决冲突): {original_filename} → {unique_filename}")
                        else:
                            if self._clean_type == '中文':
                                results['logs'].append(f"✅ 重命名并分类: {original_filename} → {folder_name}/{final_clean_filename}")
                            else:
                                results['logs'].append(f"✅ 重命名: {original_filename} → {final_clean_filename}")
                    else:
                        results['errors'] += 1
                        results['logs'].append(f"❌ 重命名失败: {original_filename}")

                    results['processed'] += 1

                except Exception as e:
                    results['errors'] += 1
                    results['logs'].append(f"❌ 处理文件 {original_filename} 时出错: {str(e)}")

        except Exception as batch_error:
            results['logs'].append(f"❌ 批次 {batch_num} 处理出错: {str(batch_error)}")

        return results

    def _safe_rename_with_retry(self, old_path, new_path, filename, max_retries=3):
        """安全的文件重命名，带重试机制"""
        for retry_count in range(max_retries):
            try:
                os.rename(old_path, new_path)
                return True
            except (OSError, PermissionError):
                if retry_count < max_retries - 1:
                    time.sleep(0.01)  # 短暂等待后重试
                else:
                    return False
        return False

    def process_folder_ultra_fast(self, folder_path, progress_callback=None, log_callback=None):
        """
        超高性能多线程文件夹处理 - 彻底解决崩溃问题
        使用多线程并行处理，大幅提升性能和稳定性
        """
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")

        # 支持的图片格式
        image_extensions = SUPPORTED_IMAGE_EXTENSIONS

        # 重置停止标志和重复文件计数器
        self._stop_requested = False
        if self._enable_duplicate_numbering:
            with self._lock:
                self._duplicate_counter.clear()  # 清空计数器，重新开始计数

        if log_callback:
            log_callback("🚀 启动超高性能多线程文件名清洗模式")
            log_callback(f"💻 使用 {self._max_workers} 个工作线程")
            log_callback(f"🔧 清洗类型: {self._clean_type}")
            log_callback("📊 开始扫描文件...")

        # 第一阶段：快速扫描文件
        image_files = []
        scan_start_time = time.time()

        try:
            with os.scandir(folder_path) as entries:
                for entry in entries:
                    if self.is_stop_requested():
                        if log_callback:
                            log_callback("⏹️ 收到停止请求，正在安全退出...")
                        return 0

                    if entry.is_file():
                        filename = entry.name
                        if os.path.splitext(filename.lower())[1] in image_extensions:
                            image_files.append(filename)

                    # 每扫描一定数量文件报告一次进度
                    if len(image_files) % MEMORY_CHECK_INTERVAL == 0 and len(image_files) > 0:
                        if log_callback:
                            log_callback(f"📁 已扫描到 {len(image_files)} 个图片文件...")

                        # 检查内存压力
                        if SystemResourceMonitor.is_memory_pressure():
                            gc.collect()
                            if log_callback:
                                log_callback("🧹 检测到内存压力，已清理内存")

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 扫描文件时出错: {str(e)}")
            raise

        scan_time = time.time() - scan_start_time
        total_files = len(image_files)

        if not image_files:
            if log_callback:
                log_callback("📂 文件夹中没有找到图片文件")
            return 0

        if log_callback:
            log_callback(f"✅ 扫描完成！找到 {total_files} 个图片文件，耗时 {scan_time:.2f} 秒")
            log_callback("🔍 开始分析文件名模式...")

        # 第二阶段：分析需要处理的文件
        files_to_process = []
        analysis_start_time = time.time()

        for filename in image_files:
            if self.is_stop_requested():
                return 0

            # 使用指定的清洗类型进行清洗
            clean_filename = self.clean_filename(filename, self._clean_type)

            # 如果启用了同名图片序号功能，所有文件都需要处理以确保正确的序号分配
            if self._enable_duplicate_numbering:
                files_to_process.append((filename, clean_filename))
            else:
                # 如果没有启用同名序号功能，只处理需要清洗的文件
                if clean_filename != filename:
                    files_to_process.append((filename, clean_filename))

        # 如果启用了同名图片序号功能，需要按宽高比排序同名文件
        if self._enable_duplicate_numbering and files_to_process:
            if log_callback:
                log_callback("📐 正在读取图片尺寸并按宽高比排序...")

            # 按清洗后的文件名分组
            groups = {}
            for original_filename, clean_filename in files_to_process:
                clean_name = os.path.splitext(clean_filename)[0]  # 去掉扩展名
                if clean_name not in groups:
                    groups[clean_name] = []
                groups[clean_name].append(original_filename)

            # 对每组同名文件按宽高比排序
            for clean_name, original_files in groups.items():
                if len(original_files) > 1:  # 只有多个同名文件才需要排序
                    # 读取每个文件的尺寸和宽高比
                    file_dimensions = []
                    for original_filename in original_files:
                        image_path = os.path.join(folder_path, original_filename)
                        width, height, aspect_ratio = UltraHighPerformanceImageOrientationProcessor.get_image_dimensions(image_path)
                        if width and height:
                            aspect_ratio = width / height if height > 0 else 0
                        else:
                            width, height, aspect_ratio = 0, 0, 0
                        file_dimensions.append((original_filename, width, height, aspect_ratio))

                    # 按宽高比倒序排序（宽高比大的在前）
                    file_dimensions.sort(key=lambda x: x[3], reverse=True)

                    # 存储排序结果
                    self._sorted_files_map[clean_name] = [item[0] for item in file_dimensions]

                    if log_callback:
                        log_callback(f"📐 {clean_name}: 按宽高比排序 {len(original_files)} 个文件")
                        for i, (filename, width, height, aspect_ratio) in enumerate(file_dimensions):
                            log_callback(f"   {i+1}. {filename} ({width}x{height}, 宽高比: {aspect_ratio:.3f})")

        analysis_time = time.time() - analysis_start_time

        if log_callback:
            if self._enable_duplicate_numbering:
                log_callback(f"📋 分析完成！找到 {len(files_to_process)} 个文件需要处理（启用同名序号功能），耗时 {analysis_time:.2f} 秒")
            else:
                log_callback(f"📋 分析完成！发现 {len(files_to_process)} 个文件需要清洗，耗时 {analysis_time:.2f} 秒")

        if not files_to_process:
            if log_callback:
                log_callback("✨ 所有文件名都已是纯数字格式，无需清洗")
            return 0

        # 第三阶段：多线程并行处理
        # 动态调整批次大小
        if len(files_to_process) <= 50:
            batch_size = 10
        elif len(files_to_process) <= 500:
            batch_size = 25
        elif len(files_to_process) <= 2000:
            batch_size = 50
        else:
            batch_size = DEFAULT_BATCH_SIZE

        # 创建批次
        batches = []
        for i in range(0, len(files_to_process), batch_size):
            batch_files = files_to_process[i:i + batch_size]
            batches.append((batch_files, folder_path, i // batch_size + 1))

        total_batches = len(batches)

        if log_callback:
            log_callback(f"⚡ 启动多线程并行处理：{total_batches} 批，每批最多 {batch_size} 个文件")
            log_callback(f"🔧 使用 {self._max_workers} 个工作线程并行处理")

        # 处理统计
        total_processed = 0
        total_renamed = 0
        total_errors = 0
        total_conflicts = 0

        processing_start_time = time.time()

        # 使用多线程处理
        try:
            with TaskManager(max_workers=self._max_workers) as task_manager:
                self._task_manager = task_manager

                # 提交所有批次任务
                for batch_info in batches:
                    if self.is_stop_requested():
                        break
                    task_manager.submit_task(self._process_file_batch, batch_info)

                # 收集结果
                completed_batches = 0
                while completed_batches < total_batches and not self.is_stop_requested():
                    # 获取已完成的任务
                    completed_tasks = task_manager.get_completed_tasks()

                    for future in completed_tasks:
                        try:
                            result = future.result()
                            completed_batches += 1

                            # 累计统计
                            total_processed += result['processed']
                            total_renamed += result['renamed']
                            total_errors += result['errors']
                            total_conflicts += result['conflicts']

                            # 输出批次日志
                            if log_callback:
                                batch_num = result['batch_num']
                                log_callback(f"✅ 第 {batch_num} 批完成：处理 {result['processed']} 个，重命名 {result['renamed']} 个")

                                # 输出详细日志（限制数量）
                                for log_msg in result['logs'][:5]:  # 只显示前5条
                                    log_callback(f"   {log_msg}")

                                if len(result['logs']) > 5:
                                    log_callback(f"   ... 还有 {len(result['logs']) - 5} 条日志")

                            # 更新进度
                            if progress_callback:
                                progress = min(100, int(completed_batches / total_batches * 100))
                                progress_callback(progress)

                            # 每10批报告一次总进度
                            if completed_batches % 10 == 0 or completed_batches == total_batches:
                                if log_callback:
                                    progress_percent = completed_batches / total_batches * 100
                                    log_callback(f"📊 总进度: {progress_percent:.1f}% ({completed_batches}/{total_batches} 批)")

                        except Exception as task_error:
                            if log_callback:
                                log_callback(f"❌ 任务执行出错: {str(task_error)}")
                            total_errors += 1

                    # 短暂休息，避免CPU占用过高
                    if completed_batches < total_batches:
                        time.sleep(0.1)

                    # 定期内存清理
                    if completed_batches % 20 == 0:
                        gc.collect()

        except Exception as processing_error:
            if log_callback:
                log_callback(f"❌ 多线程处理出错: {str(processing_error)}")
            raise
        finally:
            self._task_manager = None

        processing_time = time.time() - processing_start_time
        total_time = time.time() - scan_start_time

        # 输出最终结果
        try:
            if log_callback:
                log_callback("=" * 60)
                log_callback("🎉 超高性能多线程文件名清洗完成！")
                log_callback("=" * 60)
                log_callback(f"📊 处理结果统计：")
                log_callback(f"   总文件数: {total_files}")
                log_callback(f"   需处理文件数: {len(files_to_process)}")
                log_callback(f"   实际处理数: {total_processed}")
                log_callback(f"   成功重命名: {total_renamed}")
                log_callback(f"   解决冲突数: {total_conflicts}")
                log_callback(f"   错误文件数: {total_errors}")
                log_callback("=" * 60)
                log_callback(f"⏱️ 性能统计：")
                log_callback(f"   扫描时间: {scan_time:.2f} 秒")
                log_callback(f"   分析时间: {analysis_time:.2f} 秒")
                log_callback(f"   处理时间: {processing_time:.2f} 秒")
                log_callback(f"   总耗时: {total_time:.2f} 秒")
                if total_time > 0:
                    log_callback(f"   平均速度: {total_files/total_time:.1f} 文件/秒")
                log_callback(f"   使用线程数: {self._max_workers}")
                log_callback("=" * 60)

            # 确保最终进度为100%
            if progress_callback:
                progress_callback(100)

        except Exception as final_error:
            if log_callback:
                log_callback(f"⚠️ 最终结果输出时出错: {str(final_error)}")

        return total_renamed


# 为了兼容性，保留原来的类名
class HighPerformanceImageNameCleaner(UltraHighPerformanceImageNameCleaner):
    """兼容性类，继承超高性能清洗器"""
    pass


# 为了兼容性，保留原来的类名
class ImageNameCleaner:
    """兼容性类，提供静态方法接口"""

    @staticmethod
    def clean_filename(filename, clean_type='数字'):
        """清洗文件名的静态方法，支持多种清洗类型"""
        return UltraHighPerformanceImageNameCleaner.clean_filename(filename, clean_type)

    @staticmethod
    def process_folder(folder_path, progress_callback=None, log_callback=None, clean_type='数字'):
        """静态方法包装器，用于兼容现有代码"""
        cleaner = UltraHighPerformanceImageNameCleaner(clean_type=clean_type)
        return cleaner.process_folder_ultra_fast(folder_path, progress_callback, log_callback)


# ---------------------------
# 超高性能多线程图片方向处理器
# ---------------------------
class UltraHighPerformanceImageOrientationProcessor:
    """超高性能多线程图片方向处理器"""

    def __init__(self, max_workers=None):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(4, SystemResourceMonitor.get_cpu_count())  # 图片处理用较少线程
        self._task_manager = None

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True
            if self._task_manager:
                self._task_manager.stop()

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    @staticmethod
    def get_image_dimensions(image_path):
        """获取图片的真实宽高（优化版本）"""
        try:
            # 使用更安全的方式打开图片
            with Image.open(image_path) as img:
                # 检查图片尺寸并记录信息
                width, height = img.size
                total_pixels = width * height

                # 对于大图片进行警告和记录
                if total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                    size_mb = (total_pixels * 3) / (1024 * 1024)  # 估算RGB内存占用
                    logging.info(f"处理大图片: {os.path.basename(image_path)}, 尺寸: {width}x{height}, 像素: {total_pixels:,}, 估算内存: {size_mb:.1f}MB")

                # 处理EXIF旋转信息（优化版本）
                try:
                    # 使用更现代的方法获取EXIF数据
                    exif_dict = img.getexif()
                    if exif_dict is not None:
                        # 查找旋转信息
                        orientation = exif_dict.get(274)  # 274是Orientation标签的数字ID
                        if orientation:
                            if orientation == 3:
                                img = img.rotate(180, expand=True)
                            elif orientation == 6:
                                img = img.rotate(270, expand=True)
                            elif orientation == 8:
                                img = img.rotate(90, expand=True)
                            # 更新尺寸
                            width, height = img.size
                except (AttributeError, KeyError, TypeError, OSError):
                    # 静默处理EXIF错误
                    pass

                return (width, height)
        except Exception as e:
            logging.error(f"获取图片尺寸失败 {image_path}: {str(e)}")
            return None

    @staticmethod
    def is_portrait(image_path):
        """判断图片是否为竖向（高 > 宽）"""
        dimensions = UltraHighPerformanceImageOrientationProcessor.get_image_dimensions(image_path)
        if dimensions:
            width, height = dimensions
            return height > width
        return False

    @staticmethod
    def rotate_image_to_landscape(image_path, output_path=None):
        """将竖向图片旋转90度变为横向（超高性能版本）"""
        try:
            with Image.open(image_path) as img:
                # 获取当前尺寸
                width, height = img.size

                # 检查图片大小并记录信息
                total_pixels = width * height
                if total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                    size_mb = (total_pixels * 3) / (1024 * 1024)  # 估算RGB内存占用
                    logging.info(f"正在旋转大图片: {os.path.basename(image_path)}, 尺寸: {width}x{height}, 估算内存: {size_mb:.1f}MB")

                # 如果是竖向图片，旋转90度
                if height > width:
                    logging.info(f"旋转竖向图片: {os.path.basename(image_path)} ({width}x{height} → {height}x{width})")

                    # 顺时针旋转90度
                    rotated_img = img.rotate(-90, expand=True)

                    # 保存图片（优化参数）
                    save_path = output_path if output_path else image_path

                    # 根据图片大小调整保存参数
                    if total_pixels > 100_000_000:  # 1亿像素以上的超大图
                        # 超大图片使用较低质量以节省空间
                        rotated_img.save(save_path, quality=75, optimize=True, progressive=True)
                        logging.info(f"超大图片保存为较低质量(75%)以节省空间")
                    elif total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                        rotated_img.save(save_path, quality=85, optimize=True, progressive=True)
                    else:
                        rotated_img.save(save_path, quality=95, optimize=True)

                    return True
                else:
                    return False  # 已经是横向，无需旋转

        except Exception as e:
            logging.error(f"旋转图片失败 {image_path}: {str(e)}")
            return False
        finally:
            # 对于大图片处理后进行内存清理
            try:
                if 'total_pixels' in locals() and total_pixels > LARGE_IMAGE_PIXEL_THRESHOLD:
                    gc.collect()  # 强制垃圾回收
                    logging.debug("大图片处理完成，已清理内存")
            except:
                pass

    def _process_image_batch(self, batch_info):
        """
        处理单个图片批次 - 多线程工作函数
        """
        batch_files, folder_path, batch_num = batch_info

        results = {
            'batch_num': batch_num,
            'processed': 0,
            'rotated': 0,
            'errors': 0,
            'logs': []
        }

        try:
            for filename in batch_files:
                if self.is_stop_requested():
                    break

                try:
                    image_path = os.path.join(folder_path, filename)

                    # 检查文件是否存在
                    if not os.path.exists(image_path):
                        results['logs'].append(f"⚠️ 跳过: {filename} (文件不存在)")
                        continue

                    # 检查是否为竖向图片
                    is_portrait = False
                    try:
                        is_portrait = self.is_portrait(image_path)
                    except Exception as check_error:
                        results['logs'].append(f"❌ 检查图片方向失败: {filename} -> {str(check_error)}")
                        results['errors'] += 1
                        continue

                    if is_portrait:
                        # 旋转图片
                        success = self.rotate_image_to_landscape(image_path)

                        if success:
                            results['rotated'] += 1
                            results['logs'].append(f"✅ 旋转: {filename} (竖向 -> 横向)")
                        else:
                            results['errors'] += 1
                            results['logs'].append(f"❌ 旋转失败: {filename}")
                    else:
                        results['logs'].append(f"⏭️ 跳过: {filename} (已是横向)")

                    results['processed'] += 1

                except Exception as e:
                    results['errors'] += 1
                    results['logs'].append(f"❌ 处理文件 {filename} 时出错: {str(e)}")

        except Exception as batch_error:
            results['logs'].append(f"❌ 批次 {batch_num} 处理出错: {str(batch_error)}")

        return results


# 兼容性类
class ImageOrientationProcessor(UltraHighPerformanceImageOrientationProcessor):
    """兼容性类，继承超高性能处理器"""

    @staticmethod
    def get_image_dimensions(image_path):
        """静态方法包装器"""
        return UltraHighPerformanceImageOrientationProcessor.get_image_dimensions(image_path)

    @staticmethod
    def is_portrait(image_path):
        """静态方法包装器"""
        return UltraHighPerformanceImageOrientationProcessor.is_portrait(image_path)

    @staticmethod
    def rotate_image_to_landscape(image_path, output_path=None):
        """静态方法包装器"""
        return UltraHighPerformanceImageOrientationProcessor.rotate_image_to_landscape(image_path, output_path)

    @staticmethod
    def process_folder(folder_path, progress_callback=None, log_callback=None):
        """
        处理文件夹中的所有图片，将竖向图片转为横向
        增强版本：支持分批处理、内存优化和错误恢复
        """
        if not os.path.exists(folder_path):
            raise ValueError(f"文件夹不存在: {folder_path}")

        # 支持的图片格式
        image_extensions = SUPPORTED_IMAGE_EXTENSIONS

        # 分批获取图片文件
        if log_callback:
            log_callback("正在扫描图片文件...")

        image_files = []
        try:
            for filename in os.listdir(folder_path):
                if os.path.splitext(filename.lower())[1] in image_extensions:
                    image_files.append(filename)

                # 每扫描一定数量文件进行一次内存清理
                if len(image_files) % DEFAULT_BATCH_SIZE == 0:
                    gc.collect()
                    if log_callback:
                        log_callback(f"已扫描到 {len(image_files)} 个图片文件...")
        except Exception as e:
            if log_callback:
                log_callback(f"扫描文件时出错: {str(e)}")
            raise

        if not image_files:
            if log_callback:
                log_callback("文件夹中没有找到图片文件")
            return 0

        total_files = len(image_files)
        processed_count = 0
        rotated_count = 0
        error_count = 0

        if log_callback:
            log_callback(f"找到 {total_files} 个图片文件，开始检查和旋转...")

        # 分批处理文件，每批20个文件（图片处理更消耗内存，批次更小）
        batch_size = 20
        total_batches = (total_files + batch_size - 1) // batch_size

        if log_callback:
            log_callback(f"将分 {total_batches} 批处理，每批最多 {batch_size} 个文件")

        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_files)
            batch_files = image_files[start_idx:end_idx]

            if log_callback:
                log_callback(f"正在处理第 {batch_num + 1}/{total_batches} 批 ({len(batch_files)} 个文件)...")

            for i, filename in enumerate(batch_files):
                try:
                    image_path = os.path.join(folder_path, filename)

                    # 检查文件是否仍然存在
                    if not os.path.exists(image_path):
                        if log_callback:
                            log_callback(f"跳过: {filename} (文件不存在)")
                        continue

                    # 检查是否为竖向图片
                    is_portrait = False
                    try:
                        is_portrait = ImageOrientationProcessor.is_portrait(image_path)
                    except Exception as check_error:
                        if log_callback:
                            log_callback(f"检查图片方向失败: {filename} -> {str(check_error)}")
                        error_count += 1
                        continue

                    if is_portrait:
                        # 旋转图片，添加重试机制
                        retry_count = 0
                        max_retries = 2
                        rotation_success = False

                        while retry_count < max_retries and not rotation_success:
                            try:
                                if ImageOrientationProcessor.rotate_image_to_landscape(image_path):
                                    rotated_count += 1
                                    rotation_success = True
                                    if log_callback:
                                        log_callback(f"旋转: {filename} (竖向 -> 横向)")
                                else:
                                    if log_callback:
                                        log_callback(f"旋转失败: {filename}")
                                    break
                            except Exception as rotation_error:
                                retry_count += 1
                                if retry_count < max_retries:
                                    if log_callback:
                                        log_callback(f"旋转失败，重试 {retry_count}/{max_retries}: {filename}")
                                    import time
                                    time.sleep(0.2)  # 短暂等待后重试
                                else:
                                    if log_callback:
                                        log_callback(f"旋转失败(已重试{max_retries}次): {filename} -> {str(rotation_error)}")
                                    error_count += 1
                    else:
                        if log_callback:
                            log_callback(f"跳过: {filename} (已是横向)")

                    processed_count += 1

                    # 更新进度
                    if progress_callback:
                        overall_progress = int((start_idx + i + 1) / total_files * 100)
                        progress_callback(overall_progress)

                except Exception as e:
                    error_count += 1
                    if log_callback:
                        log_callback(f"处理文件 {filename} 时出错: {str(e)}")

            # 每批处理完成后进行内存清理
            gc.collect()
            if log_callback:
                log_callback(f"第 {batch_num + 1} 批处理完成，已清理内存")

        if log_callback:
            log_callback("=" * 50)
            log_callback("图片方向处理完成！处理结果统计：")
            log_callback(f"总文件数: {total_files}")
            log_callback(f"处理文件数: {processed_count}")
            log_callback(f"旋转文件数: {rotated_count}")
            log_callback(f"错误文件数: {error_count}")
            log_callback("=" * 50)

        return rotated_count


# ---------------------------
# 超高性能多线程图片移动器
# ---------------------------
class UltraHighPerformanceImageMover:
    """超高性能多线程图片移动器 - 将子文件夹中的图片移动到主目录"""

    def __init__(self, max_workers=None):
        self._stop_requested = False
        self._lock = threading.Lock()
        self._max_workers = max_workers or min(8, SystemResourceMonitor.get_cpu_count())
        self._task_manager = None

    def request_stop(self):
        """请求停止处理"""
        with self._lock:
            self._stop_requested = True
            if self._task_manager:
                self._task_manager.stop()

    def is_stop_requested(self):
        """检查是否请求停止"""
        with self._lock:
            return self._stop_requested

    @staticmethod
    def get_supported_image_extensions():
        """获取支持的图片格式"""
        return SUPPORTED_IMAGE_EXTENSIONS

    def scan_images_in_subfolders(self, root_path, progress_callback=None, log_callback=None):
        """
        扫描所有子文件夹中的图片文件
        返回: (image_files_list, subfolder_list)
        """
        if not os.path.exists(root_path):
            raise ValueError(f"根目录不存在: {root_path}")

        image_extensions = self.get_supported_image_extensions()
        image_files = []  # [(source_path, relative_path, filename), ...]
        subfolders = set()  # 记录所有子文件夹

        if log_callback:
            log_callback("🔍 开始扫描子文件夹中的图片文件...")

        scan_count = 0

        try:
            # 递归遍历所有子目录
            for current_root, dirs, files in os.walk(root_path):
                if self.is_stop_requested():
                    break

                # 跳过根目录本身
                if current_root == root_path:
                    continue

                # 记录子文件夹
                subfolders.add(current_root)

                # 扫描当前目录中的图片文件
                for filename in files:
                    if self.is_stop_requested():
                        break

                    # 检查是否为图片文件
                    if os.path.splitext(filename.lower())[1] in image_extensions:
                        source_path = os.path.join(current_root, filename)
                        relative_path = normalize_path_for_display(os.path.relpath(current_root, root_path))

                        image_files.append((source_path, relative_path, filename))
                        scan_count += 1

                        # 每扫描500个文件报告一次进度
                        if scan_count % 500 == 0 and log_callback:
                            log_callback(f"📁 已扫描到 {scan_count} 个图片文件...")

                        # 内存压力检查
                        if scan_count % MEMORY_CHECK_INTERVAL == 0 and SystemResourceMonitor.is_memory_pressure():
                            gc.collect()
                            if log_callback:
                                log_callback("🧹 检测到内存压力，已清理内存")

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 扫描过程中出错: {str(e)}")
            raise

        if log_callback:
            log_callback(f"✅ 扫描完成！找到 {len(image_files)} 个图片文件在 {len(subfolders)} 个子文件夹中")

        return image_files, list(subfolders)

    def _move_file_batch(self, batch_info):
        """
        移动单个文件批次 - 多线程工作函数
        """
        batch_files, root_path, batch_num = batch_info

        results = {
            'batch_num': batch_num,
            'processed': 0,
            'moved': 0,
            'overwritten': 0,
            'errors': 0,
            'logs': []
        }

        try:
            for source_path, relative_path, filename in batch_files:
                if self.is_stop_requested():
                    break

                try:
                    # 检查源文件是否存在
                    if not os.path.exists(source_path):
                        results['logs'].append(f"⚠️ 跳过: {filename} (源文件不存在)")
                        continue

                    # 目标路径（主目录）
                    target_path = os.path.join(root_path, filename)

                    # 检查是否需要覆盖
                    will_overwrite = os.path.exists(target_path)

                    # 执行移动操作
                    success = self._safe_move_with_retry(source_path, target_path, filename)

                    if success:
                        results['moved'] += 1
                        if will_overwrite:
                            results['overwritten'] += 1
                            results['logs'].append(f"✅ 移动(覆盖): {relative_path}/{filename} → {filename}")
                        else:
                            results['logs'].append(f"✅ 移动: {relative_path}/{filename} → {filename}")
                    else:
                        results['errors'] += 1
                        results['logs'].append(f"❌ 移动失败: {relative_path}/{filename}")

                    results['processed'] += 1

                except Exception as e:
                    results['errors'] += 1
                    results['logs'].append(f"❌ 处理文件 {filename} 时出错: {str(e)}")

        except Exception as batch_error:
            results['logs'].append(f"❌ 批次 {batch_num} 处理出错: {str(batch_error)}")

        return results

    def _safe_move_with_retry(self, source_path, target_path, filename, max_retries=3):
        """安全的文件移动，带重试机制"""
        for retry_count in range(max_retries):
            try:
                # 如果目标文件存在，先删除（实现覆盖）
                if os.path.exists(target_path):
                    os.remove(target_path)

                # 移动文件
                shutil.move(source_path, target_path)
                return True
            except (OSError, PermissionError, shutil.Error) as e:
                if retry_count < max_retries - 1:
                    time.sleep(0.1)  # 短暂等待后重试
                else:
                    logging.error(f"移动文件失败 {filename}: {str(e)}")
                    return False
        return False

    def remove_empty_folders(self, root_path, subfolders, log_callback=None):
        """
        删除空文件夹
        """
        if log_callback:
            log_callback("🗑️ 开始清理空文件夹...")

        removed_count = 0

        # 按深度排序，从最深的文件夹开始删除
        sorted_folders = sorted(subfolders, key=lambda x: x.count(os.sep), reverse=True)

        for folder_path in sorted_folders:
            if self.is_stop_requested():
                break

            try:
                # 检查文件夹是否存在且为空
                if os.path.exists(folder_path) and not os.listdir(folder_path):
                    os.rmdir(folder_path)
                    removed_count += 1
                    relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                    if log_callback:
                        log_callback(f"🗑️ 删除空文件夹: {relative_path}")
                elif os.path.exists(folder_path):
                    # 文件夹不为空，记录日志
                    relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                    if log_callback:
                        log_callback(f"⚠️ 跳过非空文件夹: {relative_path}")

            except Exception as e:
                relative_path = normalize_path_for_display(os.path.relpath(folder_path, root_path))
                if log_callback:
                    log_callback(f"❌ 删除文件夹失败: {relative_path} -> {str(e)}")

        if log_callback:
            log_callback(f"✅ 清理完成！删除了 {removed_count} 个空文件夹")

        return removed_count

    def process_folder_move_images(self, root_path, progress_callback=None, log_callback=None):
        """
        超高性能多线程图片移动处理 - 主要处理方法
        将所有子文件夹中的图片移动到主目录，并删除空文件夹
        """
        if not os.path.exists(root_path):
            raise ValueError(f"根目录不存在: {root_path}")

        # 重置停止标志
        self._stop_requested = False

        if log_callback:
            log_callback("🚀 启动超高性能多线程图片移动模式")
            log_callback(f"💻 使用 {self._max_workers} 个工作线程")
            log_callback(f"📁 根目录: {root_path}")

        start_time = time.time()

        try:
            # 第一阶段：扫描所有子文件夹中的图片
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第一阶段：扫描子文件夹中的图片文件")
                log_callback("=" * 60)

            image_files, subfolders = self.scan_images_in_subfolders(
                root_path, progress_callback, log_callback
            )

            if not image_files:
                if log_callback:
                    log_callback("✨ 子文件夹中没有找到图片文件，无需移动")
                return 0

            # 第二阶段：多线程并行移动图片
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第二阶段：多线程并行移动图片文件")
                log_callback("=" * 60)

            moved_count = self._process_move_images_parallel(
                image_files, root_path, progress_callback, log_callback
            )

            # 第三阶段：清理空文件夹
            if log_callback:
                log_callback("=" * 60)
                log_callback("📊 第三阶段：清理空文件夹")
                log_callback("=" * 60)

            removed_folders = self.remove_empty_folders(root_path, subfolders, log_callback)

            # 输出最终结果
            total_time = time.time() - start_time
            if log_callback:
                log_callback("=" * 60)
                log_callback("🎉 超高性能多线程图片移动完成！")
                log_callback("=" * 60)
                log_callback(f"📊 处理结果统计：")
                log_callback(f"   扫描到的图片文件: {len(image_files)}")
                log_callback(f"   成功移动的文件: {moved_count}")
                log_callback(f"   删除的空文件夹: {removed_folders}")
                log_callback(f"   总耗时: {total_time:.2f} 秒")
                if total_time > 0:
                    log_callback(f"   平均速度: {len(image_files)/total_time:.1f} 文件/秒")
                log_callback(f"   使用线程数: {self._max_workers}")
                log_callback("=" * 60)

            # 确保最终进度为100%
            if progress_callback:
                progress_callback(100)

            return moved_count

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 图片移动过程中出错: {str(e)}")
            raise

    def _process_move_images_parallel(self, image_files, root_path, progress_callback=None, log_callback=None):
        """
        多线程并行处理图片移动
        """
        total_files = len(image_files)

        # 动态调整批次大小
        if total_files <= 50:
            batch_size = 10
        elif total_files <= 500:
            batch_size = 25
        elif total_files <= 2000:
            batch_size = 50
        else:
            batch_size = DEFAULT_BATCH_SIZE

        # 创建批次
        batches = []
        for i in range(0, total_files, batch_size):
            batch_files = image_files[i:i + batch_size]
            batches.append((batch_files, root_path, i // batch_size + 1))

        total_batches = len(batches)

        if log_callback:
            log_callback(f"⚡ 启动多线程并行移动：{total_batches} 批，每批最多 {batch_size} 个文件")
            log_callback(f"🔧 使用 {self._max_workers} 个工作线程并行处理")

        # 处理统计
        total_processed = 0
        total_moved = 0
        total_overwritten = 0
        total_errors = 0

        processing_start_time = time.time()

        # 使用多线程处理
        try:
            with TaskManager(max_workers=self._max_workers) as task_manager:
                self._task_manager = task_manager

                # 提交所有批次任务
                for batch_info in batches:
                    if self.is_stop_requested():
                        break
                    task_manager.submit_task(self._move_file_batch, batch_info)

                # 收集结果
                completed_batches = 0
                while completed_batches < total_batches and not self.is_stop_requested():
                    # 获取已完成的任务
                    completed_tasks = task_manager.get_completed_tasks()

                    for future in completed_tasks:
                        try:
                            result = future.result()
                            completed_batches += 1

                            # 累计统计
                            total_processed += result['processed']
                            total_moved += result['moved']
                            total_overwritten += result['overwritten']
                            total_errors += result['errors']

                            # 输出批次日志
                            if log_callback:
                                batch_num = result['batch_num']
                                log_callback(f"✅ 第 {batch_num} 批完成：处理 {result['processed']} 个，移动 {result['moved']} 个，覆盖 {result['overwritten']} 个")

                                # 输出详细日志（限制数量）
                                for log_msg in result['logs'][:3]:  # 只显示前3条
                                    log_callback(f"   {log_msg}")

                                if len(result['logs']) > 3:
                                    log_callback(f"   ... 还有 {len(result['logs']) - 3} 条日志")

                            # 更新进度
                            if progress_callback:
                                progress = min(90, int(completed_batches / total_batches * 90))  # 移动阶段占90%
                                progress_callback(progress)

                            # 每10批报告一次总进度
                            if completed_batches % 10 == 0 or completed_batches == total_batches:
                                if log_callback:
                                    progress_percent = completed_batches / total_batches * 100
                                    log_callback(f"📊 移动进度: {progress_percent:.1f}% ({completed_batches}/{total_batches} 批)")

                        except Exception as task_error:
                            if log_callback:
                                log_callback(f"❌ 任务执行出错: {str(task_error)}")
                            total_errors += 1

                    # 短暂休息，避免CPU占用过高
                    if completed_batches < total_batches:
                        time.sleep(0.1)

                    # 定期内存清理
                    if completed_batches % 20 == 0:
                        gc.collect()

        except Exception as processing_error:
            if log_callback:
                log_callback(f"❌ 多线程移动处理出错: {str(processing_error)}")
            raise
        finally:
            self._task_manager = None

        processing_time = time.time() - processing_start_time

        if log_callback:
            log_callback("=" * 50)
            log_callback("📊 移动阶段统计：")
            log_callback(f"   处理文件数: {total_processed}")
            log_callback(f"   成功移动数: {total_moved}")
            log_callback(f"   覆盖文件数: {total_overwritten}")
            log_callback(f"   错误文件数: {total_errors}")
            log_callback(f"   移动耗时: {processing_time:.2f} 秒")
            log_callback("=" * 50)

        return total_moved


# ---------------------------
# 拖拽图片处理模块
# ---------------------------
class DragDropImageProcessor:
    """
    拖拽图片处理器
    负责处理拖拽的图片文件，执行清洗、旋转、复制和重复检测操作
    """

    @staticmethod
    def get_supported_image_extensions():
        """获取支持的图片格式"""
        return SUPPORTED_IMAGE_EXTENSIONS

    @staticmethod
    def is_image_file(file_path):
        """判断文件是否为支持的图片格式"""
        if not os.path.isfile(file_path):
            return False
        ext = os.path.splitext(file_path.lower())[1]
        return ext in DragDropImageProcessor.get_supported_image_extensions()

    @staticmethod
    def extract_image_files_from_urls(urls: List[QUrl]) -> List[str]:
        """从URL列表中提取图片文件路径"""
        image_files = []
        for url in urls:
            if url.isLocalFile():
                file_path = url.toLocalFile()
                if DragDropImageProcessor.is_image_file(file_path):
                    image_files.append(file_path)
        return image_files

    @staticmethod
    def check_duplicate_in_library_enhanced(image_path: str, library_path: str) -> dict:
        """
        增强版重复检测方法 - 优化第一次拖拽处理
        检查图片是否在图库中已存在，区分同名同宽高比和同名不同宽高比的情况
        
        优化点：
        1. 更准确的宽高比比较
        2. 更智能的文件名建议
        3. 支持第一次拖拽时的正确处理
        
        返回: {
            'has_same_name': bool,           # 是否有同名文件
            'is_same_aspect_ratio': bool,    # 是否有同名同宽高比文件
            'existing_path': str,            # 已存在的文件路径
            'clean_filename': str,           # 清洗后的文件名
            'suggested_filename': str        # 建议的文件名（用于不同宽高比的情况）
        }
        """
        try:
            original_filename = os.path.basename(image_path)
            clean_filename = ImageNameCleaner.clean_filename(original_filename)
            name, ext = os.path.splitext(clean_filename)

            # 获取当前图片的宽高比
            try:
                with Image.open(image_path) as current_img:
                    current_width, current_height = current_img.size
                    current_aspect_ratio = current_width / current_height if current_height > 0 else 0
            except Exception as e:
                logging.warning(f"无法读取当前图片宽高比: {str(e)}")
                return {
                    'has_same_name': False,
                    'is_same_aspect_ratio': False,
                    'existing_path': "",
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }

            # 检查所有可能的序号变体文件
            candidate_files = []

            # 1. 检查基础文件名
            base_path = os.path.join(library_path, clean_filename)
            if os.path.exists(base_path):
                candidate_files.append((base_path, 0))  # (路径, 序号)

            # 2. 检查序号变体文件（_2, _3, _4等）
            # 优化：检查更多可能的序号，避免遗漏
            consecutive_missing_count = 0
            max_consecutive_missing = 3  # 减少到3个连续缺失，提高检测效率

            for i in range(2, min(MAX_FILENAME_COUNTER, 20)):  # 限制检查范围到20，提高性能
                numbered_filename = f"{name}_{i}{ext}"
                numbered_path = os.path.join(library_path, numbered_filename)
                if os.path.exists(numbered_path):
                    candidate_files.append((numbered_path, i))
                    consecutive_missing_count = 0  # 重置连续缺失计数
                else:
                    consecutive_missing_count += 1
                    # 只有连续缺失超过阈值时才停止检查
                    if consecutive_missing_count >= max_consecutive_missing:
                        break

            # 如果没有找到任何候选文件，肯定不重复
            if not candidate_files:
                return {
                    'has_same_name': False,
                    'is_same_aspect_ratio': False,
                    'existing_path': "",
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }

            # 检查每个候选文件的宽高比
            tolerance = ASPECT_RATIO_TOLERANCE
            same_aspect_ratio_files = []
            different_aspect_ratio_files = []

            for candidate_path, sequence_num in candidate_files:
                try:
                    with Image.open(candidate_path) as existing_img:
                        existing_width, existing_height = existing_img.size
                        existing_aspect_ratio = existing_width / existing_height if existing_height > 0 else 0

                    # 比较宽高比
                    aspect_ratio_diff = abs(current_aspect_ratio - existing_aspect_ratio)

                    if aspect_ratio_diff < tolerance:
                        same_aspect_ratio_files.append((candidate_path, sequence_num, existing_aspect_ratio))
                    else:
                        different_aspect_ratio_files.append((candidate_path, sequence_num, existing_aspect_ratio))

                except Exception as e:
                    logging.warning(f"无法读取候选文件宽高比 {candidate_path}: {str(e)}")
                    continue

            # 有同名文件
            has_same_name = True

            if same_aspect_ratio_files:
                # 有同名同宽高比文件
                return {
                    'has_same_name': True,
                    'is_same_aspect_ratio': True,
                    'existing_path': same_aspect_ratio_files[0][0],
                    'clean_filename': clean_filename,
                    'suggested_filename': clean_filename
                }
            else:
                # 只有同名不同宽高比文件，需要智能生成新的文件名
                # 按宽高比倒序排列现有文件，然后确定当前文件的位置
                different_aspect_ratio_files.sort(key=lambda x: x[2], reverse=True)  # 按宽高比倒序

                # 优化的文件名分配策略：
                # 1. 宽高比最大的使用基础文件名
                # 2. 宽高比较小的使用_2序号
                # 3. 如果当前图片宽高比最大，需要重命名现有文件
                
                if not different_aspect_ratio_files:
                    # 理论上不应该到这里，但作为保险
                    suggested_filename = clean_filename
                else:
                    # 找到当前文件在宽高比排序中的位置
                    insert_position = 0
                    for idx, (file_path, seq_num, aspect_ratio) in enumerate(different_aspect_ratio_files):
                        if current_aspect_ratio > aspect_ratio:
                            insert_position = idx
                            break
                        insert_position = idx + 1

                    # 智能文件名分配
                    if insert_position == 0:
                        # 当前图片宽高比最大，应该使用基础文件名
                        # 但现有文件已经占用了基础文件名，所以当前图片使用基础文件名
                        # 现有的文件在实际处理时会被重命名为_2
                        suggested_filename = clean_filename
                    else:
                        # 当前图片宽高比较小，使用_2序号
                        suggested_filename = f"{name}_2{ext}"

                return {
                    'has_same_name': True,
                    'is_same_aspect_ratio': False,
                    'existing_path': different_aspect_ratio_files[0][0] if different_aspect_ratio_files else "",
                    'clean_filename': clean_filename,
                    'suggested_filename': suggested_filename
                }

        except Exception as e:
            # 如果检查失败，假设不重复
            logging.warning(f"增强重复检查失败: {str(e)}")
            original_filename = os.path.basename(image_path)
            clean_filename = ImageNameCleaner.clean_filename(original_filename)  # 使用默认数字类型
            return {
                'has_same_name': False,
                'is_same_aspect_ratio': False,
                'existing_path': "",
                'clean_filename': clean_filename,
                'suggested_filename': clean_filename
            }

    @staticmethod
    def check_duplicate_in_library(image_path: str, library_path: str) -> tuple:
        """
        检查图片是否在图库中已存在（基于宽高比的智能重复检测）
        检查所有可能的序号变体文件（如图片名字.jpg、图片名字_2.jpg等）
        只有当文件名相同且宽高比也相同时才认为是真正的重复
        返回: (is_duplicate: bool, existing_path: str, clean_filename: str)
        """
        try:
            original_filename = os.path.basename(image_path)
            clean_filename = ImageNameCleaner.clean_filename(original_filename)
            name, ext = os.path.splitext(clean_filename)
            
            # 获取当前图片的宽高比
            try:
                with Image.open(image_path) as current_img:
                    current_width, current_height = current_img.size
                    current_aspect_ratio = current_width / current_height if current_height > 0 else 0
            except Exception as e:
                logging.warning(f"无法读取当前图片宽高比: {str(e)}")
                return False, "", clean_filename
            
            # 检查所有可能的序号变体文件
            candidate_files = []
            
            # 1. 检查基础文件名
            base_path = os.path.join(library_path, clean_filename)
            if os.path.exists(base_path):
                candidate_files.append(base_path)
            
            # 2. 检查序号变体文件（_2, _3, _4等）
            # 修复：不要在遇到第一个不存在的序号时就停止，要检查所有可能的序号
            # 因为可能存在不连续的序号文件（如 mf0159.jpg 和 mf0159_3.jpg，但没有 mf0159_2.jpg）
            consecutive_missing_count = 0
            max_consecutive_missing = 5  # 允许最多5个连续缺失的序号

            for i in range(2, MAX_FILENAME_COUNTER):  # 检查到最大计数器为止
                numbered_filename = f"{name}_{i}{ext}"
                numbered_path = os.path.join(library_path, numbered_filename)
                if os.path.exists(numbered_path):
                    candidate_files.append(numbered_path)
                    consecutive_missing_count = 0  # 重置连续缺失计数
                else:
                    consecutive_missing_count += 1
                    # 只有连续缺失超过阈值时才停止检查
                    if consecutive_missing_count >= max_consecutive_missing:
                        break
            
            # 如果没有找到任何候选文件，肯定不重复
            if not candidate_files:
                return False, "", clean_filename
            
            # 检查每个候选文件的宽高比
            tolerance = ASPECT_RATIO_TOLERANCE
            for candidate_path in candidate_files:
                try:
                    with Image.open(candidate_path) as existing_img:
                        existing_width, existing_height = existing_img.size
                        existing_aspect_ratio = existing_width / existing_height if existing_height > 0 else 0
                    
                    # 比较宽高比
                    aspect_ratio_diff = abs(current_aspect_ratio - existing_aspect_ratio)
                    
                    if aspect_ratio_diff < tolerance:
                        # 找到宽高比相同的文件，认为是真正的重复
                        return True, candidate_path, clean_filename
                        
                except Exception as e:
                    logging.warning(f"无法读取候选文件宽高比 {candidate_path}: {str(e)}")
                    continue
            
            # 所有候选文件的宽高比都不同，不是重复
            return False, "", clean_filename

        except Exception as e:
            # 如果检查失败，假设不重复
            logging.warning(f"重复检查失败: {str(e)}")
            original_filename = os.path.basename(image_path)
            clean_filename = ImageNameCleaner.clean_filename(original_filename)  # 使用默认数字类型
            return False, "", clean_filename

    @staticmethod
    def _generate_smart_filename(output_dir: str, clean_filename: str, current_aspect_ratio: float, log_callback=None) -> str:
        """
        基于宽高比的智能文件名生成逻辑（简化版，避免复杂的文件重命名）
        
        Args:
            output_dir: 输出目录
            clean_filename: 清洗后的文件名
            current_aspect_ratio: 当前图片的宽高比
            log_callback: 日志回调函数
            
        Returns:
            str: 最终的文件名
        """
        try:
            name, ext = os.path.splitext(clean_filename)
            target_path = os.path.join(output_dir, clean_filename)
            
            # 如果文件不存在，直接返回原文件名
            if not os.path.exists(target_path):
                if log_callback:
                    log_callback(f"📁 文件名可用: {clean_filename}")
                return clean_filename
            
            # 文件已存在，检查宽高比
            if log_callback:
                log_callback(f"📁 检测到同名文件，分析宽高比...")
            
            # 获取现有文件的宽高比
            existing_aspect_ratio = None
            try:
                with Image.open(target_path) as img:
                    width, height = img.size
                    existing_aspect_ratio = width / height if height > 0 else 0
                    if log_callback:
                        log_callback(f"📐 现有文件宽高比: {existing_aspect_ratio:.3f} ({width}x{height})")
            except Exception as e:
                if log_callback:
                    log_callback(f"⚠️ 无法获取现有文件宽高比: {str(e)}")
                existing_aspect_ratio = 0
            
            # 比较宽高比（使用统一容差）
            if abs(current_aspect_ratio - existing_aspect_ratio) < ASPECT_RATIO_TOLERANCE:
                # 宽高比相同，替换现有文件
                if log_callback:
                    log_callback(f"🔄 宽高比相同(差值: {abs(current_aspect_ratio - existing_aspect_ratio):.4f} < {ASPECT_RATIO_TOLERANCE})，将替换现有文件")
                return clean_filename
            
            # 宽高比不同，生成序号文件名（简化逻辑，不重排现有文件）
            if log_callback:
                log_callback(f"📊 宽高比不同(差值: {abs(current_aspect_ratio - existing_aspect_ratio):.4f} >= {ASPECT_RATIO_TOLERANCE})，生成序号文件名")
            
            # 收集所有同名文件及其宽高比，但不重排现有文件
            existing_files_info = []
            
            # 检查基础文件
            existing_files_info.append((clean_filename, existing_aspect_ratio))
            
            # 检查所有序号文件
            counter = 2
            while counter <= MAX_FILENAME_COUNTER:  # 限制检查范围，避免无限循环
                numbered_filename = f"{name}_{counter}{ext}"
                numbered_path = os.path.join(output_dir, numbered_filename)
                
                if os.path.exists(numbered_path):
                    try:
                        with Image.open(numbered_path) as img:
                            width, height = img.size
                            aspect_ratio = width / height if height > 0 else 0
                            existing_files_info.append((numbered_filename, aspect_ratio))
                            if log_callback:
                                log_callback(f"📐 发现序号文件 {numbered_filename}: 宽高比 {aspect_ratio:.3f}")
                    except Exception:
                        existing_files_info.append((numbered_filename, 0))
                
                counter += 1
            
            # 按宽高比倒序排序现有文件
            existing_files_info.sort(key=lambda x: x[1], reverse=True)
            
            # 找到当前图片在排序中的位置
            insert_position = 0
            for i, (filename, aspect_ratio) in enumerate(existing_files_info):
                if current_aspect_ratio > aspect_ratio:
                    insert_position = i
                    break
                insert_position = i + 1
            
            if log_callback:
                log_callback(f"📍 当前图片按宽高比应排在第 {insert_position + 1} 位")
            
            # 优化策略：确保宽高比倒序排列（大的在前，小的在后）
            # 如果当前图片宽高比最大，使用基础文件名（覆盖）
            if insert_position == 0:
                if log_callback:
                    log_callback(f"🔄 当前图片宽高比最大({current_aspect_ratio:.3f})，将使用基础文件名")
                return clean_filename
            else:
                # 当前图片不是宽高比最大的，使用_2序号
                # 按照用户要求，只保留两个版本：基础名和_2
                candidate_filename = f"{name}_2{ext}"
                if log_callback:
                    log_callback(f"📁 当前图片宽高比较小({current_aspect_ratio:.3f})，使用序号文件名: {candidate_filename}")
                return candidate_filename
                
                # 如果所有序号都被占用，使用时间戳
                import time
                timestamp = int(time.time() * 1000) % 100000
                fallback_filename = f"{name}_{timestamp}{ext}"
                if log_callback:
                    log_callback(f"📁 使用时间戳文件名: {fallback_filename}")
                return fallback_filename
            
        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 智能文件名生成失败: {str(e)}，使用备用方案")
            
            # 备用方案：使用传统的序号逻辑
            name, ext = os.path.splitext(clean_filename)
            counter = 1
            while True:
                if counter == 1:
                    new_filename = clean_filename
                else:
                    new_filename = f"{name}_{counter}{ext}"
                
                new_path = os.path.join(output_dir, new_filename)
                if not os.path.exists(new_path):
                    return new_filename
                counter += 1
                
                if counter > MAX_FILENAME_COUNTER:  # 防止无限循环
                    import time
                    timestamp = int(time.time() * 1000) % 100000
                    return f"{name}_{timestamp}{ext}"

    @staticmethod
    def process_single_image_with_suggested_name(image_path: str, output_dir: str, suggested_filename: str = None, clean_type: str = '数字', log_callback=None) -> tuple:
        """
        处理单个图片文件：清洗文件名 + 横向放置 + 复制到目标文件夹（支持建议文件名和中文分类）
        返回: (success: bool, processed_filename: str, message: str)
        """
        original_filename = ""
        try:
            # 规范化路径格式，使用反斜杠
            image_path = image_path.replace('/', '\\')
            output_dir = output_dir.replace('/', '\\')

            # 调试信息：开始处理
            if log_callback:
                log_callback(f"🔍 开始处理图片: {image_path}")

            # 检查输入文件
            if not os.path.exists(image_path):
                error_msg = f"文件不存在: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 确保目标文件夹存在
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    if log_callback:
                        log_callback(f"📁 创建目标文件夹: {output_dir}")
                except Exception as e:
                    error_msg = f"无法创建目标文件夹 {output_dir}: {str(e)}"
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, "", error_msg

            # 检查文件是否为图片格式
            if not DragDropImageProcessor.is_image_file(image_path):
                error_msg = f"不支持的文件格式: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 获取原始文件名
            original_filename = os.path.basename(image_path)
            if log_callback:
                log_callback(f"📁 原始文件名: {original_filename}")

            # 1. 确定最终文件名和目标文件夹
            if suggested_filename:
                final_filename = suggested_filename
                if log_callback:
                    log_callback(f"📝 使用建议文件名: {final_filename}")
            else:
                # 清洗文件名
                try:
                    final_filename = ImageNameCleaner.clean_filename(original_filename, clean_type)
                    if log_callback:
                        if final_filename != original_filename:
                            log_callback(f"🧹 文件名清洗: {original_filename} → {final_filename}")
                        else:
                            log_callback(f"✅ 文件名无需清洗: {original_filename}")
                except Exception as clean_error:
                    error_msg = f"文件名清洗失败: {str(clean_error)}"
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, "", error_msg

            # 2. 中文类型特殊处理：根据文件名确定子文件夹
            final_output_dir = output_dir
            if clean_type == '中文':
                # 获取应该归类的文件夹名
                folder_name = get_folder_name_for_chinese_filename(final_filename)
                final_output_dir = os.path.join(output_dir, folder_name)
                
                # 确保子文件夹存在
                if not os.path.exists(final_output_dir):
                    try:
                        os.makedirs(final_output_dir, exist_ok=True)
                        if log_callback:
                            log_callback(f"📁 创建分类文件夹: {folder_name}")
                    except Exception as e:
                        error_msg = f"无法创建分类文件夹 {final_output_dir}: {str(e)}"
                        if log_callback:
                            log_callback(f"❌ {error_msg}")
                        return False, "", error_msg
                
                if log_callback:
                    log_callback(f"📂 中文分类: {final_filename} → {folder_name} 文件夹")

            # 2. 创建临时文件进行处理
            try:
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_file_path = os.path.join(temp_dir, final_filename)
                    if log_callback:
                        log_callback(f"📂 创建临时文件: {temp_file_path}")

                    # 复制原文件到临时位置
                    shutil.copy2(image_path, temp_file_path)
                    if log_callback:
                        log_callback(f"📋 文件复制到临时位置完成")

                    # 3. 检查并旋转图片（如果需要）
                    try:
                        if log_callback:
                            log_callback(f"🔄 检查图片方向...")

                        is_portrait = ImageOrientationProcessor.is_portrait(temp_file_path)
                        if log_callback:
                            orientation = "竖向" if is_portrait else "横向"
                            log_callback(f"📐 图片方向: {orientation}")

                        if is_portrait:
                            if log_callback:
                                log_callback(f"🔄 开始旋转图片...")

                            rotation_success = ImageOrientationProcessor.rotate_image_to_landscape(temp_file_path)
                            if rotation_success:
                                if log_callback:
                                    log_callback(f"✅ 图片旋转成功: {final_filename} (竖向 → 横向)")
                            else:
                                if log_callback:
                                    log_callback(f"⚠️ 图片旋转失败: {final_filename}")
                        else:
                            if log_callback:
                                log_callback(f"✅ 图片无需旋转: {final_filename} (已是横向)")
                    except Exception as rotation_error:
                        if log_callback:
                            log_callback(f"⚠️ 图片旋转处理失败: {final_filename}, 错误: {str(rotation_error)}")
                        # 继续处理，不因为旋转失败而停止整个流程

                    # 4. 复制到目标文件夹
                    try:
                        final_output_path = os.path.join(final_output_dir, final_filename)

                        if log_callback:
                            log_callback(f"📁 最终文件名: {final_filename}")
                            log_callback(f"📍 目标路径: {final_output_path}")

                        shutil.copy2(temp_file_path, final_output_path)

                        if log_callback:
                            log_callback(f"✅ 文件已成功复制到: {final_output_path}")

                        return True, final_filename, "处理成功"

                    except Exception as copy_error:
                        error_msg = f"复制文件到目标位置失败: {str(copy_error)}"
                        if log_callback:
                            log_callback(f"❌ {error_msg}")
                        return False, "", error_msg

            except Exception as temp_error:
                error_msg = f"临时文件处理失败: {str(temp_error)}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

        except Exception as e:
            error_msg = f"处理文件 {original_filename or image_path} 时出错: {str(e)}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
                # 添加详细的错误堆栈信息
                import traceback
                log_callback(f"🔍 详细错误信息: {traceback.format_exc()}")
            return False, "", error_msg
        finally:
            # 在处理完成后进行内存清理
            try:
                gc.collect()
            except:
                pass
            
    @staticmethod
    def process_single_image(image_path: str, output_dir: str, log_callback=None) -> tuple:
        """
        处理单个图片文件：清洗文件名 + 横向放置 + 复制到目标文件夹
        返回: (success: bool, processed_filename: str, message: str)
        """
        original_filename = ""
        try:
            # 规范化路径格式，使用反斜杠
            image_path = image_path.replace('/', '\\')
            output_dir = output_dir.replace('/', '\\')

            # 调试信息：开始处理
            if log_callback:
                log_callback(f"🔍 开始处理图片: {image_path}")

            # 检查输入文件
            if not os.path.exists(image_path):
                error_msg = f"文件不存在: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 确保目标文件夹存在
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    if log_callback:
                        log_callback(f"📁 创建目标文件夹: {output_dir}")
                except Exception as e:
                    error_msg = f"无法创建目标文件夹 {output_dir}: {str(e)}"
                    if log_callback:
                        log_callback(f"❌ {error_msg}")
                    return False, "", error_msg

            # 检查文件是否为图片格式
            if not DragDropImageProcessor.is_image_file(image_path):
                error_msg = f"不支持的文件格式: {image_path}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 获取原始文件名
            original_filename = os.path.basename(image_path)
            if log_callback:
                log_callback(f"📁 原始文件名: {original_filename}")

            # 1. 清洗文件名（使用默认数字类型，拖拽处理中会传递正确的清洗类型）
            try:
                clean_filename = ImageNameCleaner.clean_filename(original_filename)  # 使用默认数字类型
                if log_callback:
                    if clean_filename != original_filename:
                        log_callback(f"🧹 文件名清洗: {original_filename} → {clean_filename}")
                    else:
                        log_callback(f"✅ 文件名无需清洗: {original_filename}")
            except Exception as clean_error:
                error_msg = f"文件名清洗失败: {str(clean_error)}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

            # 2. 创建临时文件进行处理
            try:
                with tempfile.TemporaryDirectory() as temp_dir:
                    temp_file_path = os.path.join(temp_dir, clean_filename)
                    if log_callback:
                        log_callback(f"📂 创建临时文件: {temp_file_path}")

                    # 复制原文件到临时位置
                    shutil.copy2(image_path, temp_file_path)
                    if log_callback:
                        log_callback(f"📋 文件复制到临时位置完成")

                    # 3. 检查并旋转图片（如果需要）
                    try:
                        if log_callback:
                            log_callback(f"🔄 检查图片方向...")

                        is_portrait = ImageOrientationProcessor.is_portrait(temp_file_path)
                        if log_callback:
                            orientation = "竖向" if is_portrait else "横向"
                            log_callback(f"📐 图片方向: {orientation}")

                        if is_portrait:
                            if log_callback:
                                log_callback(f"🔄 开始旋转图片...")

                            rotation_success = ImageOrientationProcessor.rotate_image_to_landscape(temp_file_path)
                            if rotation_success:
                                if log_callback:
                                    log_callback(f"✅ 图片旋转成功: {clean_filename} (竖向 → 横向)")
                            else:
                                if log_callback:
                                    log_callback(f"⚠️ 图片旋转失败: {clean_filename}")
                        else:
                            if log_callback:
                                log_callback(f"✅ 图片无需旋转: {clean_filename} (已是横向)")
                    except Exception as rotation_error:
                        if log_callback:
                            log_callback(f"⚠️ 图片旋转处理失败: {clean_filename}, 错误: {str(rotation_error)}")
                        # 继续处理，不因为旋转失败而停止整个流程

                    # 4. 基于宽高比的智能文件名生成并复制到目标文件夹
                    try:
                        if log_callback:
                            log_callback(f"📝 基于宽高比生成智能文件名...")

                        # 获取当前图片的宽高比
                        current_aspect_ratio = None
                        try:
                            with Image.open(temp_file_path) as img:
                                width, height = img.size
                                current_aspect_ratio = width / height if height > 0 else 0
                                if log_callback:
                                    log_callback(f"📐 当前图片宽高比: {current_aspect_ratio:.3f} ({width}x{height})")
                        except Exception as e:
                            if log_callback:
                                log_callback(f"⚠️ 无法获取图片宽高比: {str(e)}")
                            current_aspect_ratio = 0

                        # 使用智能文件名生成逻辑
                        unique_filename = DragDropImageProcessor._generate_smart_filename(
                            output_dir, clean_filename, current_aspect_ratio, log_callback
                        )
                        final_output_path = os.path.join(output_dir, unique_filename)

                        if log_callback:
                            log_callback(f"📁 最终文件名: {unique_filename}")
                            log_callback(f"📍 目标路径: {final_output_path}")

                        shutil.copy2(temp_file_path, final_output_path)

                        if log_callback:
                            log_callback(f"✅ 文件已成功复制到: {final_output_path}")

                        return True, unique_filename, "处理成功"

                    except Exception as copy_error:
                        error_msg = f"复制文件到目标位置失败: {str(copy_error)}"
                        if log_callback:
                            log_callback(f"❌ {error_msg}")
                        return False, "", error_msg

            except Exception as temp_error:
                error_msg = f"临时文件处理失败: {str(temp_error)}"
                if log_callback:
                    log_callback(f"❌ {error_msg}")
                return False, "", error_msg

        except Exception as e:
            error_msg = f"处理文件 {original_filename or image_path} 时出错: {str(e)}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
                # 添加详细的错误堆栈信息
                import traceback
                log_callback(f"🔍 详细错误信息: {traceback.format_exc()}")
            return False, "", error_msg
        finally:
            # 在处理完成后进行内存清理
            try:
                gc.collect()
            except:
                pass


class DragDropArea(QLabel):
    """
    支持拖拽的区域组件
    """
    files_dropped = pyqtSignal(list)  # 发送拖拽的文件列表

    def __init__(self, text=""):
        super().__init__(text)
        self.setAcceptDrops(True)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMinimumHeight(120)
        self.setWordWrap(True)

        # 设置样式
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #cccccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)

        # 设置默认文本
        if not text:
            self.setText("""
🖼️ 拖拽图片到此区域

支持单个或多个图片文件
自动执行：名字清洗 + 横向放置 + 复制到选择的文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
            """)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含图片文件
            urls = event.mimeData().urls()
            image_files = DragDropImageProcessor.extract_image_files_from_urls(urls)
            if image_files:
                event.acceptProposedAction()
                # 更新样式以显示可以放置
                self.setStyleSheet("""
                    QLabel {
                        border: 2px dashed #4CAF50;
                        border-radius: 8px;
                        background-color: #e8f5e8;
                        color: #4CAF50;
                        font-size: 14px;
                        padding: 20px;
                        font-weight: bold;
                    }
                """)
                self.setText(f"📥 准备接收 {len(image_files)} 个图片文件")
            else:
                event.ignore()
        else:
            event.ignore()

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        _ = event  # 标记参数已使用
        # 恢复原始样式
        self.setStyleSheet("""
            QLabel {
                border: 2px dashed #cccccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)
        self.setText("""
🖼️ 拖拽图片到此区域

支持单个或多个图片文件
自动执行：名字清洗 + 横向放置 + 复制到选择的文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
        """)

    def dropEvent(self, event: QDropEvent):
        """拖拽放置事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            image_files = DragDropImageProcessor.extract_image_files_from_urls(urls)
            if image_files:
                event.acceptProposedAction()
                self.files_dropped.emit(image_files)

                # 恢复原始样式
                self.dragLeaveEvent(None)
            else:
                event.ignore()
        else:
            event.ignore()


class DragDropProcessorWorker(QThread):
    """
    拖拽图片处理工作线程
    支持重复检测和策略处理
    优化：分批处理同名不同宽高比图片，确保第一次拖拽也能正确显示
    """
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str, int, dict, dict)  # success, message, processed_count, statistics, file_details
    duplicate_found = pyqtSignal(str, str, str)  # image_path, existing_path, clean_filename

    def __init__(self, image_files: List[str], output_dir: str, check_duplicates: bool = True, duplicate_strategy: str = "overwrite", clean_type: str = '数字'):
        super().__init__()
        self.image_files = image_files
        self.output_dir = output_dir
        self.check_duplicates = check_duplicates
        self.duplicate_strategy = duplicate_strategy  # "overwrite" 或 "skip"
        self.clean_type = clean_type  # 清洗类型
        self.duplicate_actions = {}  # 存储用户对重复文件的选择

        # 统计信息
        self.statistics = {
            'total_files': 0,
            'processed_files': 0,
            'success_files': 0,
            'skipped_files': 0,
            'error_files': 0,
            'same_aspect_ratio_overwritten': 0,  # 同名同宽高比覆盖的文件数
            'different_aspect_ratio_renamed': 0,  # 同名不同宽高比重命名的文件数
            'new_files_processed': 0,  # 新文件处理数（没有同名冲突的文件）
        }

        # 详细文件列表跟踪
        self.file_details = {
            'skipped_files': [],  # 被跳过的文件：[{'original_name': str, 'reason': str}, ...]
            'overwritten_files': [],  # 被覆盖的文件：[{'original_name': str, 'target_name': str, 'reason': str}, ...]
            'new_files': [],  # 新添加的文件：[{'original_name': str, 'target_name': str}, ...]
            'renamed_files': [],  # 重命名的文件：[{'original_name': str, 'target_name': str, 'reason': str}, ...]
            'error_files': [],  # 处理失败的文件：[{'original_name': str, 'error': str}, ...]
        }

    def set_duplicate_action(self, image_path: str, action: str):
        """设置重复文件的处理动作"""
        self.duplicate_actions[image_path] = action

    def _group_images_by_clean_name_and_aspect_ratio(self, image_files: List[str]) -> dict:
        """
        按清洗后的文件名和宽高比对图片进行分组
        返回: {
            'clean_name': {
                'same_aspect_ratio': [files],  # 同名同宽高比的文件
                'different_aspect_ratio': [files],  # 同名不同宽高比的文件
                'new_files': [files]  # 新文件（没有同名冲突）
            }
        }
        """
        groups = {}

        # 第一步：收集所有文件的基本信息
        file_infos = []
        for image_path in image_files:
            try:
                # 获取清洗后的文件名，使用指定的清洗类型
                original_filename = os.path.basename(image_path)
                clean_filename = ImageNameCleaner.clean_filename(original_filename, self.clean_type)
                clean_name = os.path.splitext(clean_filename)[0]

                # 获取当前图片的宽高比
                try:
                    with Image.open(image_path) as img:
                        width, height = img.size
                        current_aspect_ratio = width / height if height > 0 else 0
                except Exception as e:
                    self.log.emit(f"⚠️ 无法获取图片宽高比: {original_filename}, 错误: {str(e)}")
                    current_aspect_ratio = 0

                file_infos.append({
                    'path': image_path,
                    'original_filename': original_filename,
                    'clean_filename': clean_filename,
                    'clean_name': clean_name,
                    'aspect_ratio': current_aspect_ratio
                })

            except Exception as e:
                self.log.emit(f"❌ 分析图片时出错: {os.path.basename(image_path)}, 错误: {str(e)}")

        # 第二步：按clean_name分组并分析冲突
        for file_info in file_infos:
            clean_name = file_info['clean_name']

            # 初始化分组
            if clean_name not in groups:
                groups[clean_name] = {
                    'same_aspect_ratio': [],
                    'different_aspect_ratio': [],
                    'new_files': []
                }

            # 检查图库中是否已存在同名文件
            duplicate_result = DragDropImageProcessor.check_duplicate_in_library_enhanced(
                file_info['path'], self.output_dir
            )

            # 检查当前批次中是否有其他同名文件
            current_batch_same_name_files = [
                f for f in file_infos
                if f['clean_name'] == clean_name and f['path'] != file_info['path']
            ]

            has_library_same_name = duplicate_result['has_same_name']
            has_batch_same_name = len(current_batch_same_name_files) > 0

            if has_library_same_name:
                # 图库中有同名文件
                if duplicate_result['is_same_aspect_ratio']:
                    # 同名同宽高比
                    groups[clean_name]['same_aspect_ratio'].append({
                        'path': file_info['path'],
                        'aspect_ratio': file_info['aspect_ratio'],
                        'clean_filename': file_info['clean_filename'],
                        'suggested_filename': duplicate_result['suggested_filename']
                    })
                else:
                    # 同名不同宽高比
                    groups[clean_name]['different_aspect_ratio'].append({
                        'path': file_info['path'],
                        'aspect_ratio': file_info['aspect_ratio'],
                        'clean_filename': file_info['clean_filename'],
                        'suggested_filename': duplicate_result['suggested_filename']
                    })
            elif has_batch_same_name:
                # 图库中没有同名文件，但当前批次中有同名文件，归类为不同宽高比处理
                groups[clean_name]['different_aspect_ratio'].append({
                    'path': file_info['path'],
                    'aspect_ratio': file_info['aspect_ratio'],
                    'clean_filename': file_info['clean_filename'],
                    'suggested_filename': file_info['clean_filename']  # 临时文件名，后续会重新分配
                })
            else:
                # 既没有图库同名文件，也没有批次同名文件，归类为新文件
                groups[clean_name]['new_files'].append({
                    'path': file_info['path'],
                    'aspect_ratio': file_info['aspect_ratio'],
                    'clean_filename': file_info['clean_filename'],
                    'suggested_filename': file_info['clean_filename']
                })

        return groups

    def _process_batch_with_delay(self, batch_files: List[dict], batch_name: str, delay_seconds: float = 0.5) -> tuple:
        """
        处理一批文件，支持延迟处理
        """
        batch_success = 0
        batch_errors = 0
        
        self.log.emit(f"🔄 开始处理{batch_name}批次，共 {len(batch_files)} 个文件")
        
        for i, file_info in enumerate(batch_files):
            try:
                image_path = file_info['path']
                suggested_filename = file_info['suggested_filename']
                
                # 处理单个图片，传递清洗类型参数
                success, processed_filename, message = DragDropImageProcessor.process_single_image_with_suggested_name(
                    image_path, self.output_dir, suggested_filename, self.clean_type, self.log.emit
                )
                
                if success:
                    batch_success += 1
                    self.log.emit(f"✅ {batch_name}批次处理成功: {processed_filename}")

                    # 记录成功处理的文件详细信息
                    original_name = os.path.basename(image_path)
                    if batch_name == "同名同宽高比":
                        self.file_details['overwritten_files'].append({
                            'original_name': original_name,
                            'target_name': processed_filename,
                            'reason': '同名同宽高比文件，用户选择覆盖策略'
                        })
                    elif batch_name == "新文件":
                        self.file_details['new_files'].append({
                            'original_name': original_name,
                            'target_name': processed_filename
                        })
                    elif "同名不同宽高比" in batch_name:
                        if original_name != processed_filename:
                            self.file_details['renamed_files'].append({
                                'original_name': original_name,
                                'target_name': processed_filename,
                                'reason': '同名不同宽高比，按宽高比倒序重命名'
                            })
                        else:
                            self.file_details['new_files'].append({
                                'original_name': original_name,
                                'target_name': processed_filename
                            })
                else:
                    batch_errors += 1
                    self.log.emit(f"❌ {batch_name}批次处理失败: {message}")

                    # 记录错误文件详细信息
                    original_name = os.path.basename(image_path)
                    self.file_details['error_files'].append({
                        'original_name': original_name,
                        'error': message
                    })
                
                # 添加小延迟，确保文件系统操作完成
                if delay_seconds > 0 and i < len(batch_files) - 1:
                    time.sleep(delay_seconds)
                    
            except Exception as e:
                batch_errors += 1
                error_msg = str(e)
                self.log.emit(f"❌ {batch_name}批次处理文件时出错: {error_msg}")

                # 记录错误文件详细信息
                original_name = os.path.basename(file_info['path'])
                self.file_details['error_files'].append({
                    'original_name': original_name,
                    'error': error_msg
                })
        
        self.log.emit(f"✅ {batch_name}批次处理完成: 成功 {batch_success} 个，失败 {batch_errors} 个")
        return batch_success, batch_errors

    def _collect_existing_files_info(self, clean_name: str, output_dir: str) -> list:
        """
        收集图库中所有同名文件的信息
        返回: [{'path': str, 'aspect_ratio': float, 'is_new': False}, ...]
        """
        existing_files_info = []

        try:
            # 检查基础文件名（检查所有支持的扩展名）
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp']:
                base_path = os.path.join(output_dir, f"{clean_name}{ext}")
                if os.path.exists(base_path):
                    try:
                        with Image.open(base_path) as img:
                            width, height = img.size
                            aspect_ratio = width / height if height > 0 else 0
                            existing_files_info.append({
                                'path': base_path,
                                'aspect_ratio': aspect_ratio,
                                'is_new': False
                            })
                    except Exception:
                        existing_files_info.append({
                            'path': base_path,
                            'aspect_ratio': 0,
                            'is_new': False
                        })
                    break  # 找到一个就停止

            # 检查序号文件
            counter = 2
            consecutive_missing = 0
            while counter <= MAX_FILENAME_COUNTER and consecutive_missing < 5:
                found_any = False
                for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp']:
                    numbered_path = os.path.join(output_dir, f"{clean_name}_{counter}{ext}")
                    if os.path.exists(numbered_path):
                        try:
                            with Image.open(numbered_path) as img:
                                width, height = img.size
                                aspect_ratio = width / height if height > 0 else 0
                                existing_files_info.append({
                                    'path': numbered_path,
                                    'aspect_ratio': aspect_ratio,
                                    'is_new': False
                                })
                        except Exception:
                            existing_files_info.append({
                                'path': numbered_path,
                                'aspect_ratio': 0,
                                'is_new': False
                            })
                        found_any = True
                        break

                if found_any:
                    consecutive_missing = 0
                else:
                    consecutive_missing += 1

                counter += 1

        except Exception as e:
            self.log.emit(f"⚠️ 收集现有文件信息时出错: {str(e)}")

        return existing_files_info

    def _create_different_aspect_ratio_batches(self, different_aspect_groups: dict) -> list:
        """
        创建同名不同宽高比文件的分批处理列表
        每个批次中，每个clean_name只包含一个文件

        Args:
            different_aspect_groups: {clean_name: [file_info_list], ...}

        Returns:
            list: [[batch1_files], [batch2_files], ...]
        """
        batches = []

        # 找出最大的文件数量，确定需要多少个批次
        max_files_per_name = max(len(files) for files in different_aspect_groups.values()) if different_aspect_groups else 0

        if max_files_per_name == 0:
            return batches

        # 为每个批次创建文件列表
        for batch_index in range(max_files_per_name):
            batch_files = []

            for clean_name, files in different_aspect_groups.items():
                if batch_index < len(files):
                    # 当前批次包含这个clean_name的第batch_index个文件
                    batch_files.append(files[batch_index])

            if batch_files:
                batches.append(batch_files)

        # 记录分批信息
        self.log.emit(f"📦 分批策略:")
        for clean_name, files in different_aspect_groups.items():
            if len(files) > 1:
                self.log.emit(f"   {clean_name}: {len(files)} 个文件将分 {len(files)} 个批次处理")
                for i, file_info in enumerate(files):
                    filename = os.path.basename(file_info['path'])
                    aspect_ratio = file_info['aspect_ratio']
                    self.log.emit(f"     批次{i+1}: {filename} (宽高比: {aspect_ratio:.3f})")
            else:
                filename = os.path.basename(files[0]['path'])
                aspect_ratio = files[0]['aspect_ratio']
                self.log.emit(f"   {clean_name}: 1 个文件 - {filename} (宽高比: {aspect_ratio:.3f})")

        return batches

    def run(self):
        processed_files = []  # 记录已处理的文件，用于错误回滚
        try:
            total_files = len(self.image_files)
            self.statistics['total_files'] = total_files

            self.log.emit(f"🚀 开始优化处理 {total_files} 个图片文件...")
            self.log.emit(f"📁 目标文件夹: {self.output_dir}")
            if self.check_duplicates:
                strategy_text = "覆盖" if self.duplicate_strategy == "overwrite" else "跳过"
                self.log.emit(f"🔍 启用重复检测功能，同名同宽高比策略：{strategy_text}")
            self.log.emit("=" * 60)

            # 规范化输出目录路径
            self.output_dir = self.output_dir.replace('/', '\\')

            # 第一步：按清洗后的文件名和宽高比对图片进行分组
            self.log.emit("📊 第一步：分析图片文件，按同名同宽高比和同名不同宽高比分组...")
            groups = self._group_images_by_clean_name_and_aspect_ratio(self.image_files)
            
            # 统计分组信息
            total_same_aspect_ratio = 0
            total_different_aspect_ratio = 0
            total_new_files = 0
            for clean_name, group in groups.items():
                same_count = len(group['same_aspect_ratio'])
                diff_count = len(group['different_aspect_ratio'])
                new_count = len(group['new_files'])
                total_same_aspect_ratio += same_count
                total_different_aspect_ratio += diff_count
                total_new_files += new_count

                if same_count > 0 or diff_count > 0 or new_count > 0:
                    self.log.emit(f"📋 {clean_name}: 同名同宽高比 {same_count} 个，同名不同宽高比 {diff_count} 个，新文件 {new_count} 个")

            self.log.emit(f"📈 分组完成：同名同宽高比总计 {total_same_aspect_ratio} 个，同名不同宽高比总计 {total_different_aspect_ratio} 个，新文件总计 {total_new_files} 个")
            self.log.emit("=" * 60)

            # 第二步：先处理同名同宽高比的文件（根据策略跳过或覆盖）
            self.log.emit("🔄 第二步：处理同名同宽高比文件...")
            same_aspect_ratio_files = []
            for clean_name, group in groups.items():
                same_aspect_ratio_files.extend(group['same_aspect_ratio'])
            
            if same_aspect_ratio_files:
                if self.duplicate_strategy == "skip":
                    # 跳过策略：直接跳过所有同名同宽高比文件
                    skipped_count = len(same_aspect_ratio_files)
                    self.statistics['skipped_files'] += skipped_count
                    self.statistics['processed_files'] += skipped_count

                    # 记录跳过的文件详细信息
                    for file_info in same_aspect_ratio_files:
                        original_name = os.path.basename(file_info['path'])
                        self.file_details['skipped_files'].append({
                            'original_name': original_name,
                            'reason': '同名同宽高比文件，用户选择跳过策略'
                        })

                    self.log.emit(f"⏭️ 跳过策略：跳过 {skipped_count} 个同名同宽高比文件")
                else:
                    # 覆盖策略：处理同名同宽高比文件
                    batch_success, batch_errors = self._process_batch_with_delay(
                        same_aspect_ratio_files, "同名同宽高比", 0.3
                    )
                    self.statistics['success_files'] += batch_success
                    self.statistics['error_files'] += batch_errors
                    self.statistics['processed_files'] += len(same_aspect_ratio_files)
                    self.statistics['same_aspect_ratio_overwritten'] += batch_success
            else:
                self.log.emit("✅ 没有同名同宽高比文件需要处理")

            # 更新进度（第二步完成33%）
            self.progress.emit(33)
            self.log.emit("=" * 60)

            # 第三步：处理新文件（没有同名冲突的文件）
            self.log.emit("📄 第三步：处理新文件...")
            new_files = []
            for clean_name, group in groups.items():
                new_files.extend(group['new_files'])

            if new_files:
                batch_success, batch_errors = self._process_batch_with_delay(
                    new_files, "新文件", 0.3
                )
                self.statistics['success_files'] += batch_success
                self.statistics['error_files'] += batch_errors
                self.statistics['processed_files'] += len(new_files)
                self.statistics['new_files_processed'] += batch_success
            else:
                self.log.emit("✅ 没有新文件需要处理")

            # 更新进度（第三步完成66%）
            self.progress.emit(66)
            self.log.emit("=" * 60)

            # 第四步：处理同名不同宽高比的文件（分组处理，每组一个同名不同宽高比图片）
            self.log.emit("📝 第四步：处理同名不同宽高比文件...")

            # 收集所有同名不同宽高比的文件，按clean_name分组
            different_aspect_groups = {}
            for clean_name, group in groups.items():
                if group['different_aspect_ratio']:
                    different_aspect_groups[clean_name] = group['different_aspect_ratio']

            if different_aspect_groups:
                # 将同名不同宽高比的文件分成多个批次，每个批次每个clean_name只包含一个文件
                batches = self._create_different_aspect_ratio_batches(different_aspect_groups)

                self.log.emit(f"📦 将同名不同宽高比文件分为 {len(batches)} 个批次处理，每批次每个同名只包含一个文件")

                total_batch_success = 0
                total_batch_errors = 0

                for batch_index, batch_files in enumerate(batches):
                    self.log.emit(f"🔄 开始处理第 {batch_index + 1}/{len(batches)} 批次...")

                    # 为当前批次的文件分配正确的序号
                    processed_batch_files = []
                    for file_info in batch_files:
                        clean_name = os.path.splitext(file_info['clean_filename'])[0]

                        # 收集图库中所有同名文件的信息（包括现有的和前面批次已处理的）
                        existing_files_info = self._collect_existing_files_info(clean_name, self.output_dir)

                        # 收集当前批次及后续批次中同名的文件（用于完整的宽高比排序）
                        # 注意：不包括前面批次已处理的文件，因为它们已经在existing_files_info中了
                        remaining_batch_same_name_files = []
                        for batch_idx in range(batch_index, len(batches)):
                            for other_file_info in batches[batch_idx]:
                                other_clean_name = os.path.splitext(other_file_info['clean_filename'])[0]
                                if other_clean_name == clean_name:
                                    remaining_batch_same_name_files.append({
                                        'path': other_file_info['path'],
                                        'aspect_ratio': other_file_info['aspect_ratio'],
                                        'is_new': True
                                    })

                        # 将所有相关文件合并（现有文件 + 当前及后续批次的同名文件）
                        all_files_info = existing_files_info.copy()
                        all_files_info.extend(remaining_batch_same_name_files)

                        # 按宽高比倒序排序（宽高比大的在前）
                        all_files_info.sort(key=lambda x: x['aspect_ratio'], reverse=True)

                        # 为当前文件分配正确的序号
                        for i, sorted_file_info in enumerate(all_files_info):
                            if sorted_file_info.get('is_new', False) and sorted_file_info['path'] == file_info['path']:
                                if i == 0:
                                    # 宽高比最大的使用基础文件名
                                    file_info['suggested_filename'] = file_info['clean_filename']
                                else:
                                    # 其他的使用对应序号
                                    name, ext = os.path.splitext(file_info['clean_filename'])
                                    file_info['suggested_filename'] = f"{name}_{i + 1}{ext}"
                                break

                        processed_batch_files.append(file_info)

                        # 记录排序信息
                        filename = os.path.basename(file_info['path'])
                        aspect_ratio = file_info['aspect_ratio']
                        suggested = file_info['suggested_filename']
                        position = next(i for i, x in enumerate(all_files_info) if x.get('is_new', False) and x['path'] == file_info['path']) + 1
                        self.log.emit(f"   批次{batch_index + 1} - {clean_name}: 位置{position}. {filename} (宽高比: {aspect_ratio:.3f}) → {suggested}")

                    # 处理当前批次
                    if processed_batch_files:
                        # 添加延迟确保前面的文件操作完全完成
                        if batch_index > 0:
                            self.log.emit("⏳ 等待前一批次文件系统操作完成...")
                            time.sleep(1.0)  # 1秒延迟

                        batch_success, batch_errors = self._process_batch_with_delay(
                            processed_batch_files, f"同名不同宽高比-批次{batch_index + 1}", 0.5
                        )
                        total_batch_success += batch_success
                        total_batch_errors += batch_errors

                        self.log.emit(f"✅ 第 {batch_index + 1} 批次完成: 成功 {batch_success} 个，失败 {batch_errors} 个")

                # 更新总体统计
                self.statistics['success_files'] += total_batch_success
                self.statistics['error_files'] += total_batch_errors
                self.statistics['processed_files'] += sum(len(batch) for batch in batches)
                self.statistics['different_aspect_ratio_renamed'] += total_batch_success

                self.log.emit(f"🎯 同名不同宽高比文件处理完成: 总成功 {total_batch_success} 个，总失败 {total_batch_errors} 个")
            else:
                self.log.emit("✅ 没有同名不同宽高比文件需要处理")

            # 更新进度到100%
            self.progress.emit(100)
            self.log.emit("=" * 60)

            # 输出最终统计
            self.log.emit(f"🎉 拖拽处理完成！")
            self.log.emit(f"📊 处理统计：")
            self.log.emit(f"   总文件数: {self.statistics['total_files']}")
            self.log.emit(f"   处理成功: {self.statistics['success_files']}")
            self.log.emit(f"   跳过文件: {self.statistics['skipped_files']}")
            self.log.emit(f"   处理失败: {self.statistics['error_files']}")
            if self.statistics['new_files_processed'] > 0:
                self.log.emit(f"   新文件处理: {self.statistics['new_files_processed']}")
            if self.statistics['same_aspect_ratio_overwritten'] > 0:
                self.log.emit(f"   同名同宽高比覆盖: {self.statistics['same_aspect_ratio_overwritten']}")
            if self.statistics['different_aspect_ratio_renamed'] > 0:
                self.log.emit(f"   同名不同宽高比重命名: {self.statistics['different_aspect_ratio_renamed']}")
            self.log.emit("=" * 60)

            if self.statistics['success_files'] > 0:
                message = f"成功处理 {self.statistics['success_files']} 个图片文件！"
                if self.statistics['skipped_files'] > 0:
                    message += f"（跳过 {self.statistics['skipped_files']} 个文件）"
                self.finished.emit(True, message, self.statistics['success_files'], self.statistics, self.file_details)
            else:
                if self.statistics['skipped_files'] > 0:
                    self.finished.emit(True, f"跳过了 {self.statistics['skipped_files']} 个文件，没有新文件需要处理", 0, self.statistics, self.file_details)
                else:
                    self.finished.emit(False, "没有文件处理成功", 0, self.statistics, self.file_details)

        except Exception as e:
            self.log.emit(f"❌ 拖拽处理过程中发生严重错误: {str(e)}")
            import traceback
            self.log.emit(f"🔍 详细错误信息: {traceback.format_exc()}")

            # 如果发生严重错误，记录已处理的文件信息
            if processed_files:
                self.log.emit(f"已成功处理 {len(processed_files)} 个文件")

            self.finished.emit(False, f"处理失败: {str(e)}", 0, self.statistics, self.file_details)


class IndexLibraryWorker(QThread):
    """
    图库索引工作线程
    负责图库索引的建立、维护和查询
    """
    progress = pyqtSignal(int)  # 进度信号 (0-100)
    log = pyqtSignal(str)       # 日志信号
    status = pyqtSignal(str)    # 状态信号
    finished = pyqtSignal(bool, str, int)  # 完成信号 (success, message, indexed_count)
    index_status = pyqtSignal(bool, int, str)  # 索引状态信号 (is_indexed, count, status_text)

    def __init__(self, library_path, operation_type='build_index', fast_mode=False):
        super().__init__()
        self.library_path = library_path
        self.operation_type = operation_type  # 'build_index', 'check_status', 'update_index'
        self.fast_mode = fast_mode
        self._stop_requested = False
        self.indexer = None

    def request_stop(self):
        """请求停止索引操作"""
        self._stop_requested = True
        if self.indexer:
            self.indexer.stop_indexing()
        self.log.emit("🛑 收到停止请求，正在安全停止索引...")

    def run(self):
        """线程运行方法"""
        try:
            if not HAS_IMAGE_INDEXER:
                self.finished.emit(False, "图库索引器模块未找到，无法执行索引操作", 0)
                return

            # 创建索引器实例
            self.indexer = ImageIndexerDuckDB(fast_mode=self.fast_mode)

            # 连接索引器信号
            self.indexer.progress_signal.connect(self.progress.emit)
            self.indexer.status_signal.connect(self.status.emit)
            self.indexer.error_signal.connect(self.log.emit)

            if self.operation_type == 'build_index':
                self._build_index()
            elif self.operation_type == 'check_status':
                self._check_status()
            elif self.operation_type == 'update_index':
                self._update_index()
            else:
                self.finished.emit(False, f"未知的操作类型: {self.operation_type}", 0)

        except Exception as e:
            error_msg = f"索引操作失败: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)

    def _build_index(self):
        """建立图库索引"""
        try:
            self.log.emit("🚀 开始建立图库索引...")
            self.log.emit(f"📁 图库路径: {self.library_path}")

            mode_text = "快速模式" if self.fast_mode else "完整模式"
            self.log.emit(f"🔧 索引模式: {mode_text}")

            # 执行索引建立
            success, message, indexed_count = self.indexer.scan_library(
                self.library_path,
                fast_mode=self.fast_mode
            )

            if success:
                self.log.emit(f"✅ 索引建立完成！共索引 {indexed_count} 个图片文件")
                self.index_status.emit(True, indexed_count, f"已索引 {indexed_count} 个文件")
                self.finished.emit(True, f"成功建立图库索引，共索引 {indexed_count} 个文件", indexed_count)
            else:
                self.log.emit(f"❌ 索引建立失败: {message}")
                self.finished.emit(False, f"索引建立失败: {message}", 0)

        except Exception as e:
            error_msg = f"建立索引时出错: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)

    def _check_status(self):
        """检查索引状态"""
        try:
            self.log.emit("🔍 正在检查图库索引状态...")

            # 检查是否已建立索引
            is_indexed = self.indexer.is_indexed(self.library_path)

            if is_indexed:
                # 获取索引统计信息
                if self.indexer.set_library_path(self.library_path):
                    try:
                        result = self.indexer.db.execute("SELECT COUNT(*) FROM image_files").fetchone()
                        count = result[0] if result else 0

                        self.log.emit(f"✅ 图库已建立索引，共有 {count} 个文件")
                        self.index_status.emit(True, count, f"已索引 {count} 个文件")
                        self.finished.emit(True, f"图库已建立索引，共有 {count} 个文件", count)
                    except Exception as db_error:
                        self.log.emit(f"⚠️ 读取索引统计信息失败: {str(db_error)}")
                        self.index_status.emit(True, 0, "索引存在但统计失败")
                        self.finished.emit(True, "图库已建立索引，但无法获取统计信息", 0)
                else:
                    self.log.emit("⚠️ 索引文件存在但无法连接")
                    self.index_status.emit(False, 0, "索引文件损坏")
                    self.finished.emit(False, "索引文件存在但无法连接，可能已损坏", 0)
            else:
                self.log.emit("📝 图库尚未建立索引")
                self.index_status.emit(False, 0, "未建立索引")
                self.finished.emit(True, "图库尚未建立索引", 0)

        except Exception as e:
            error_msg = f"检查索引状态时出错: {str(e)}"
            self.log.emit(error_msg)
            self.index_status.emit(False, 0, "状态检查失败")
            self.finished.emit(False, error_msg, 0)

    def _update_index(self):
        """更新图库索引（增量更新）"""
        try:
            self.log.emit("🔄 开始更新图库索引...")

            # 先检查是否已有索引
            if not self.indexer.is_indexed(self.library_path):
                self.log.emit("📝 图库尚未建立索引，将执行完整索引建立...")
                self._build_index()
                return

            # 执行增量更新（重新扫描整个库）
            # 注意：当前的ImageIndexerDuckDB实现会清空现有数据重新索引
            # 这实际上是完整重建，但保持了接口的一致性
            success, message, indexed_count = self.indexer.scan_library(
                self.library_path,
                fast_mode=self.fast_mode
            )

            if success:
                self.log.emit(f"✅ 索引更新完成！共索引 {indexed_count} 个图片文件")
                self.index_status.emit(True, indexed_count, f"已索引 {indexed_count} 个文件")
                self.finished.emit(True, f"成功更新图库索引，共索引 {indexed_count} 个文件", indexed_count)
            else:
                self.log.emit(f"❌ 索引更新失败: {message}")
                self.finished.emit(False, f"索引更新失败: {message}", 0)

        except Exception as e:
            error_msg = f"更新索引时出错: {str(e)}"
            self.log.emit(error_msg)
            self.finished.emit(False, error_msg, 0)


class HighPerformanceImageProcessorWorker(QThread):
    """高性能图片处理工作线程 - 专门优化大批量处理"""
    progress = pyqtSignal(int)
    log = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    memory_status = pyqtSignal(str)  # 内存状态信号

    def __init__(self, folder_path, operation_type, enable_duplicate_numbering=False, clean_type='数字'):
        super().__init__()
        self.folder_path = folder_path
        self.operation_type = operation_type  # 'clean_names', 'rotate_images', 或 'move_images'
        self.enable_duplicate_numbering = enable_duplicate_numbering
        self.clean_type = clean_type  # 清洗类型
        self._stop_requested = False
        self._cleaner = HighPerformanceImageNameCleaner()
        self._mover = UltraHighPerformanceImageMover()

    def request_stop(self):
        """请求停止处理"""
        self._stop_requested = True
        self._cleaner.request_stop()
        self._mover.request_stop()
        self.log.emit("🛑 收到停止请求，正在安全停止...")

    def run(self):
        """
        线程运行方法 - 增强版本，修复完成时崩溃问题
        """
        renamed_count = 0
        rotated_count = 0
        start_time = time.time()

        try:
            self.log.emit(f"🚀 开始执行 {self.operation_type} 操作...")
            self.log.emit(f"📁 目标文件夹: {self.folder_path}")

            # 报告初始内存状态
            try:
                memory_info = SystemResourceMonitor.get_memory_usage()
                if 'percent' in memory_info:
                    self.memory_status.emit(f"初始内存使用: {memory_info['percent']:.1f}%")
            except Exception as memory_error:
                self.log.emit(f"⚠️ 内存状态检查失败: {str(memory_error)}")

            if self.operation_type == 'clean_names':
                try:
                    # 使用超高性能清洗器，支持同名图片序号功能和清洗类型选择
                    ultra_cleaner = UltraHighPerformanceImageNameCleaner(
                        enable_duplicate_numbering=self.enable_duplicate_numbering,
                        clean_type=self.clean_type
                    )
                    renamed_count = ultra_cleaner.process_folder_ultra_fast(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 文件名清洗完成！重命名了 {renamed_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已重命名 {renamed_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as clean_error:
                    error_msg = f"❌ 文件名清洗过程中出错: {str(clean_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            elif self.operation_type == 'rotate_images':
                try:
                    rotated_count = ImageOrientationProcessor.process_folder(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 图片方向处理完成！旋转了 {rotated_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已旋转 {rotated_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as rotate_error:
                    error_msg = f"❌ 图片旋转过程中出错: {str(rotate_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            elif self.operation_type == 'move_images':
                try:
                    moved_count = self._mover.process_folder_move_images(
                        self.folder_path,
                        progress_callback=self._safe_progress_emit,
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 图片移动处理完成！移动了 {moved_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已移动 {moved_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as move_error:
                    error_msg = f"❌ 图片移动过程中出错: {str(move_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            elif self.operation_type == 'move_and_clean':
                try:
                    # 第一阶段：移动图片
                    self._safe_log_emit("🚀 第一阶段：开始移动图片...")
                    moved_count = self._mover.process_folder_move_images(
                        self.folder_path,
                        progress_callback=lambda p: self._safe_progress_emit(p // 2),  # 移动阶段占50%进度
                        log_callback=self._safe_log_emit
                    )

                    if self._stop_requested:
                        interrupt_msg = f"⏹️ 操作被用户中断，已移动 {moved_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)
                        return

                    # 第二阶段：清洗文件名
                    self._safe_log_emit("🧹 第二阶段：开始清洗文件名...")
                    ultra_cleaner = UltraHighPerformanceImageNameCleaner(
                        enable_duplicate_numbering=self.enable_duplicate_numbering,
                        clean_type=self.clean_type
                    )
                    renamed_count = ultra_cleaner.process_folder_ultra_fast(
                        self.folder_path,
                        progress_callback=lambda p: self._safe_progress_emit(50 + p // 2),  # 清洗阶段占50%进度
                        log_callback=self._safe_log_emit
                    )

                    end_time = time.time()
                    total_time = end_time - start_time

                    # 安全的完成信号发送
                    if not self._stop_requested:
                        success_msg = f"🎉 图片移动并清洗完成！移动了 {moved_count} 个文件，重命名了 {renamed_count} 个文件，耗时 {total_time:.2f} 秒"
                        self._safe_finished_emit(True, success_msg)
                    else:
                        interrupt_msg = f"⏹️ 操作被用户中断，已移动 {moved_count} 个文件，重命名 {renamed_count} 个文件"
                        self._safe_finished_emit(False, interrupt_msg)

                except Exception as move_clean_error:
                    error_msg = f"❌ 图片移动并清洗过程中出错: {str(move_clean_error)}"
                    self._safe_log_emit(error_msg)
                    self._safe_finished_emit(False, error_msg)

            else:
                error_msg = f"❌ 未知的操作类型: {self.operation_type}"
                self._safe_finished_emit(False, error_msg)

        except Exception as e:
            error_msg = f"❌ 线程运行过程中发生严重错误: {str(e)}"
            self._safe_log_emit(error_msg)
            logging.error(error_msg, exc_info=True)  # 记录完整的错误堆栈
            self._safe_finished_emit(False, error_msg)
        finally:
            # 确保在任何情况下都进行安全的资源清理
            self._safe_cleanup()

    def _safe_progress_emit(self, progress):
        """安全的进度信号发送"""
        try:
            if not self.isFinished():
                # 确保进度值在有效范围内
                safe_progress = min(100, max(0, int(progress)))
                self.progress.emit(safe_progress)
        except Exception as e:
            # 进度更新失败不应该影响主要处理流程
            pass

    def _safe_log_emit(self, message):
        """安全的日志信号发送"""
        try:
            if not self.isFinished():
                self.log.emit(str(message))
        except Exception as e:
            # 日志发送失败不应该影响主要处理流程
            pass

    def _safe_finished_emit(self, success, message):
        """安全的完成信号发送"""
        try:
            if not self.isFinished():
                self.finished.emit(bool(success), str(message))
        except Exception as e:
            # 如果正常的完成信号发送失败，尝试发送错误信号
            try:
                self.finished.emit(False, f"信号发送失败: {str(e)}")
            except:
                pass

    def _safe_cleanup(self):
        """安全的资源清理"""
        try:
            # 内存清理
            gc.collect()

            # 报告最终内存状态
            try:
                memory_info = SystemResourceMonitor.get_memory_usage()
                if 'percent' in memory_info:
                    self.memory_status.emit(f"最终内存使用: {memory_info['percent']:.1f}%")
            except Exception as memory_error:
                self._safe_log_emit(f"⚠️ 最终内存状态检查失败: {str(memory_error)}")

            self._safe_log_emit("🧹 已清理内存资源")

        except Exception as cleanup_error:
            try:
                self._safe_log_emit(f"⚠️ 资源清理时出现问题: {str(cleanup_error)}")
            except:
                # 如果连日志都发送不了，就静默处理
                pass


# 为了兼容性，保留原来的类名
class ImageProcessorWorker(HighPerformanceImageProcessorWorker):
    """兼容性类，继承高性能处理器"""
    pass


# ---------------------------
# UI层
# ---------------------------
class ImageProcessorApp(QMainWindow):
    def __init__(self):
        super().__init__()
        # 设置主窗口实例引用
        set_main_window_instance(self)
        # 初始化SupabaseHelper引用
        self.supabase_helper = None
        self.init_ui()
        # 更新窗口标题以显示登录用户
        self.update_window_title()
        self.worker = None
        self.drag_drop_worker = None  # 拖拽处理工作线程
        self.index_worker = None     # 图库索引工作线程

        # 加载保存的图库路径配置
        self.load_library_path_config()

    def update_window_title(self):
        """更新窗口标题，显示登录用户名"""
        title = f"{ROBOT_SMART_NAME} v{ROBOT_CURRENT_VERSION}"

        # 添加用户信息
        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                email = self.supabase_helper.get_user_email()
                if email:
                    title += f" - 用户: {email}"
            except Exception as e:
                logging.error(f"获取用户信息时出错: {e}")

        self.setWindowTitle(title)

    def check_version(self):
        """检查应用版本并更新窗口标题"""
        # 设置默认标题
        app_title = ROBOT_SMART_NAME

        if self.supabase_helper and self.supabase_helper.is_connected():
            try:
                # 尝试获取配置，先检查用户是否已登录
                if self.supabase_helper.is_authenticated():
                    log.info("用户已登录，使用已认证客户端获取配置")
                else:
                    log.info("用户未登录，将尝试使用匿名客户端获取配置")

                config = self.supabase_helper.fetch_config(IMAGE_FINDER_BOT_TAG)
                if not config:
                    log.error("未能获取云端配置信息")
                    # 即使获取失败也设置默认标题
                    self.setWindowTitle(app_title)

                    # 如果用户已登录但仍然获取失败，可能是权限问题
                    if self.supabase_helper.is_authenticated():
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "已登录但无法获取云端配置，可能是权限问题，将使用默认配置"
                        )
                    else:
                        QMessageBox.warning(
                            self,
                            "配置获取警告",
                            "无法获取云端配置，将使用默认配置"
                        )
                    return  # 使用默认配置继续运行，而不是退出

                # 成功获取配置
                latest_version = config.get('image_tool_ver')
                if not latest_version:
                    log.warning("云端配置中没有版本信息，将使用当前版本")
                    latest_version = ROBOT_CURRENT_VERSION

                log.info(f"当前应用版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                if ROBOT_CURRENT_VERSION != latest_version:
                    msg = QMessageBox()
                    msg.setIcon(QMessageBox.Icon.Critical)  # 使用错误图标
                    msg.setText("版本不匹配")
                    msg.setInformativeText(f"当前版本 {ROBOT_CURRENT_VERSION} 与云端版本 {latest_version} 不匹配，请升级到最新版本后再使用")
                    msg.setWindowTitle("版本错误")
                    msg.setStandardButtons(QMessageBox.StandardButton.Ok)
                    msg.exec()
                    # 强制退出应用
                    log.error(f"版本不匹配，强制退出应用。当前版本: {ROBOT_CURRENT_VERSION}, 云端版本: {latest_version}")
                    sys.exit(1)

                # 更新应用标题
                latest_title = config.get('app_title')
                if latest_title:
                    app_title = latest_title + '-v' + latest_version

                # 添加用户邮箱到标题
                if self.supabase_helper.is_authenticated():
                    user_email = self.supabase_helper.get_user_email()
                    if user_email:
                        app_title = f"{app_title} - {user_email}"
                        log.info(f"已将用户邮箱 {user_email} 添加到窗口标题")

                self.setWindowTitle(app_title)

            except Exception as e:
                log.error(f"云端配置检查失败: {type(e).__name__}: {str(e)}")
                # 异常情况下也设置默认标题
                self.setWindowTitle(ROBOT_SMART_NAME)
                QMessageBox.warning(
                    self,
                    "配置检查警告",
                    f"获取云端配置时出现异常，将使用默认配置"
                )
                # 不强制退出，允许用户继续使用
        else:
            # 如果没有连接到Supabase，使用默认标题
            log.warning("Supabase未连接，使用默认标题")
            self.setWindowTitle(app_title)

    def closeEvent(self, event):
        """处理窗口关闭事件，优雅退出程序"""
        _ = event  # 标记参数已使用
        try:
            # 登出Supabase
            try:
                if self.supabase_helper and self.supabase_helper.is_connected():
                    self.supabase_helper.logout()
                    logging.info("已成功登出Supabase")
            except Exception as e:
                logging.error(f"登出Supabase时出错: {e}")

            # 停止所有正在运行的任务
            if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
                self.worker.terminate()
                self.worker.wait()  # 等待线程结束

            if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning():
                self.drag_drop_worker.terminate()
                self.drag_drop_worker.wait()  # 等待拖拽处理线程结束

            if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
                self.index_worker.terminate()
                self.index_worker.wait()  # 等待索引工作线程结束

            # 确保所有日志都被写入
            for handler in log.handlers[:]:
                handler.close()
                log.removeHandler(handler)

            # 退出应用
            QApplication.quit()
        except Exception as e:
            log.error(f"关闭程序时发生错误: {e}")
            sys.exit(1)  # 如果优雅退出失败，强制退出

    def init_ui(self):
        """初始化UI元素"""
        # 设置窗口标题和大小
        self.setWindowTitle('图片处理工具')
        self.setGeometry(300, 300, 800, 600)

        # 创建顶层容器
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # ----- 顶部说明和操作区域 -----
        top_group = QGroupBox("操作区域")
        top_layout = QVBoxLayout()

        # 选择图库文件夹（统一文件夹）
        folder_layout = QHBoxLayout()
        folder_label = QLabel("图库文件夹:")
        self.folder_path_edit = QLineEdit()
        self.folder_path_edit.setReadOnly(True)
        self.folder_path_edit.setPlaceholderText("选择图库文件夹 - 既可批量处理，也可作为拖拽目标")
        browse_button = QPushButton("选择图库文件夹")
        browse_button.clicked.connect(self.select_library_folder)

        folder_layout.addWidget(folder_label)
        folder_layout.addWidget(self.folder_path_edit)
        folder_layout.addWidget(browse_button)

        top_layout.addLayout(folder_layout)

        # 功能按钮区域
        button_layout = QHBoxLayout()

        # 移动并清洗合并功能按钮
        self.move_and_clean_button = QPushButton("1. 移动并清洗")
        self.move_and_clean_button.setMinimumHeight(50)
        self.move_and_clean_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.move_and_clean_button.clicked.connect(self.move_and_clean_images)
        self.move_and_clean_button.setEnabled(False)

        self.rotate_images_button = QPushButton("2. 图片横向放置")
        self.rotate_images_button.setMinimumHeight(50)
        self.rotate_images_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.rotate_images_button.clicked.connect(self.rotate_images)
        self.rotate_images_button.setEnabled(False)

        # 图库索引管理按钮
        self.index_library_button = QPushButton("3. 图库索引管理")
        self.index_library_button.setMinimumHeight(50)
        self.index_library_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.index_library_button.clicked.connect(self.manage_library_index)
        self.index_library_button.setEnabled(False)

        # 添加停止按钮
        self.stop_button = QPushButton("停止处理")
        self.stop_button.setMinimumHeight(50)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_button.clicked.connect(self.stop_processing)
        self.stop_button.setEnabled(False)

        button_layout.addWidget(self.move_and_clean_button)
        button_layout.addWidget(self.rotate_images_button)
        button_layout.addWidget(self.index_library_button)
        button_layout.addWidget(self.stop_button)

        top_layout.addLayout(button_layout)

        # 添加清洗设置区域
        settings_group = QGroupBox("清洗设置")
        settings_main_layout = QVBoxLayout()

        # 第一行：同名图片序号开关
        numbering_layout = QHBoxLayout()
        self.duplicate_numbering_checkbox = QCheckBox("同名图片添加序号（如：71914003.jpg → 71914003_2.jpg）")
        self.duplicate_numbering_checkbox.setChecked(True)  # 默认开启
        self.duplicate_numbering_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #666666;
                margin: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #cccccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        numbering_layout.addWidget(self.duplicate_numbering_checkbox)
        numbering_layout.addStretch()
        settings_main_layout.addLayout(numbering_layout)

        # 第二行：清洗类型选择
        clean_type_layout = QHBoxLayout()
        clean_type_label = QLabel("清洗类型:")
        clean_type_label.setStyleSheet("font-size: 12px; color: #666666; margin: 5px;")
        
        # 创建清洗类型按钮组
        from PyQt6.QtWidgets import QRadioButton, QButtonGroup
        self.clean_type_button_group = QButtonGroup()
        
        self.digital_radio = QRadioButton("数字类型")
        self.digital_radio.setChecked(True)  # 默认选择数字类型
        self.digital_radio.setToolTip("保留字母前缀+数字组合，如：O0027水蜜桃.jpg → O0027.jpg")
        
        self.letter_radio = QRadioButton("字母类型")
        self.letter_radio.setToolTip("保留纯字母或字母开头的字母数字组合，如：Photo123中文.jpg → Photo123.jpg")
        
        self.chinese_radio = QRadioButton("中文类型")
        self.chinese_radio.setToolTip("保留纯中文或字母中文组合，如：美女123.jpg → 美女.jpg")

        # 设置按钮样式
        radio_style = """
            QRadioButton {
                font-size: 11px;
                color: #666666;
                margin: 2px 8px 2px 2px;
            }
            QRadioButton::indicator {
                width: 14px;
                height: 14px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #cccccc;
                background-color: white;
                border-radius: 7px;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 7px;
            }
        """

        self.digital_radio.setStyleSheet(radio_style)
        self.letter_radio.setStyleSheet(radio_style)
        self.chinese_radio.setStyleSheet(radio_style)

        # 添加到按钮组
        self.clean_type_button_group.addButton(self.digital_radio, 0)
        self.clean_type_button_group.addButton(self.letter_radio, 1)
        self.clean_type_button_group.addButton(self.chinese_radio, 2)

        clean_type_layout.addWidget(clean_type_label)
        clean_type_layout.addWidget(self.digital_radio)
        clean_type_layout.addWidget(self.letter_radio)
        clean_type_layout.addWidget(self.chinese_radio)
        clean_type_layout.addStretch()
        
        settings_main_layout.addLayout(clean_type_layout)
        
        # 第三行：清洗示例说明
        example_layout = QHBoxLayout()
        self.example_label = QLabel("示例：O0027水蜜桃.jpg → O0027.jpg")
        self.example_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #888888;
                font-style: italic;
                margin: 2px 5px;
                padding: 3px 8px;
                background-color: #f5f5f5;
                border-radius: 3px;
            }
        """)
        example_layout.addWidget(self.example_label)
        example_layout.addStretch()
        settings_main_layout.addLayout(example_layout)
        
        settings_group.setLayout(settings_main_layout)
        top_layout.addWidget(settings_group)
        
        # 连接信号，当选择改变时更新示例和保存标记
        self.clean_type_button_group.buttonClicked.connect(self.update_clean_type_example)
        self.clean_type_button_group.buttonClicked.connect(self.on_clean_type_changed)

        # 初始化示例显示
        self.update_clean_type_example()

        # 隐藏同名同宽高比图片处理策略设置，默认使用"跳过"策略
        # 创建策略选择按钮组（隐藏UI，但保留功能）
        from PyQt6.QtWidgets import QRadioButton, QButtonGroup
        self.strategy_button_group = QButtonGroup()

        self.overwrite_radio = QRadioButton("覆盖")
        self.overwrite_radio.setVisible(False)  # 隐藏UI

        self.skip_radio = QRadioButton("跳过")
        self.skip_radio.setChecked(True)  # 默认选择跳过
        self.skip_radio.setVisible(False)  # 隐藏UI

        self.strategy_button_group.addButton(self.overwrite_radio, 0)
        self.strategy_button_group.addButton(self.skip_radio, 1)

        # 添加图库索引状态显示区域
        index_status_group = QGroupBox("图库索引状态")
        index_status_layout = QHBoxLayout()

        # 索引状态标签
        self.index_status_label = QLabel("索引状态: 未检查")
        self.index_status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666666;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f9f9f9;
            }
        """)

        # 索引统计标签
        self.index_count_label = QLabel("图片数量: --")
        self.index_count_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #666666;
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #f9f9f9;
            }
        """)

        index_status_layout.addWidget(self.index_status_label)
        index_status_layout.addWidget(self.index_count_label)
        index_status_layout.addStretch()  # 添加弹性空间

        index_status_group.setLayout(index_status_layout)
        top_layout.addWidget(index_status_group)

        # 添加拖拽区域（红框区域）
        drag_drop_group = QGroupBox("功能说明")
        drag_drop_layout = QVBoxLayout()

        # 创建拖拽区域
        self.drag_drop_area = DragDropArea()
        self.drag_drop_area.files_dropped.connect(self.handle_dropped_files)

        # 添加红色边框样式（对应截图中的红框）
        self.drag_drop_area.setStyleSheet("""
            QLabel {
                border: 2px dashed #ff4444;
                border-radius: 8px;
                background-color: #fff5f5;
                color: #666666;
                font-size: 14px;
                padding: 20px;
            }
            QLabel:hover {
                border-color: #4CAF50;
                background-color: #f0f8f0;
                color: #4CAF50;
            }
        """)

        # 设置初始拖拽区域文本
        self.update_drag_drop_area_text()

        drag_drop_layout.addWidget(self.drag_drop_area)

        # 添加详细功能说明
        detailed_explanation = QLabel("""
功能说明：
1. 图片移动整理：将所有子文件夹中的图片移动到主目录，删除空文件夹
   • 递归扫描所有子文件夹中的图片文件
   • 同名文件自动覆盖原文件
   • 移动完成后自动删除空的子文件夹
2. 名字清洗：智能清洗图片文件名为纯数字格式
   • 支持中文字符清洗：71914204德洛丽丝.jpg → 71914204.jpg
   • 支持特殊符号清洗：71914204_abc.jpg → 71914204.jpg
   • 自动解决文件名冲突：重复时添加序号后缀
3. 图片横向放置：检测竖向图片并旋转90度，使所有图片都横向显示
4. 图库索引管理：建立和维护图片库索引，支持快速查询
   • 建立图片文件索引（文件名、尺寸、创建时间等）
   • 支持快速模式和完整模式索引
   • 提供索引状态检查和增量更新功能
5. 拖拽功能：直接拖拽图片到上方区域，自动执行清洗+旋转+复制+索引更新
        """)
        detailed_explanation.setWordWrap(True)
        detailed_explanation.setStyleSheet("color: #666; font-size: 12px; margin: 10px;")
        drag_drop_layout.addWidget(detailed_explanation)

        drag_drop_group.setLayout(drag_drop_layout)
        top_layout.addWidget(drag_drop_group)

        top_group.setLayout(top_layout)

        # ----- 日志和状态区域 -----
        log_group = QGroupBox("处理日志")
        log_layout = QVBoxLayout()

        # 进度条
        progress_layout = QHBoxLayout()
        progress_label = QLabel("进度:")
        self.progress_bar = QProgressBar()
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        log_layout.addLayout(progress_layout)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        log_group.setLayout(log_layout)

        # ----- 组装主界面 -----
        main_layout.addWidget(top_group)
        main_layout.addWidget(log_group)

        self.setCentralWidget(main_widget)

        # 更新日志
        self.update_log("程序已启动，请选择图库文件夹")
        self.update_log("功能：1.图片移动整理 2.名字清洗 3.图片横向放置 4.拖拽图片到红框区域进行处理")

    def select_library_folder(self):
        """选择图库文件夹（统一文件夹）"""
        # 获取上次选择的路径作为默认路径
        last_path = self.folder_path_edit.text() if self.folder_path_edit.text() else os.path.expanduser('~')

        folder = QFileDialog.getExistingDirectory(self, '选择图库文件夹', last_path)
        if folder:
            self.folder_path_edit.setText(folder)
            self.log_text.clear()
            self.progress_bar.setValue(0)
            self.update_log(f"已选择图库文件夹: {folder}")

            # 启用功能按钮
            self.rotate_images_button.setEnabled(True)
            self.move_and_clean_button.setEnabled(True)
            self.index_library_button.setEnabled(True)

            # 更新拖拽区域的提示文本
            self.update_drag_drop_area_text()

            # 检查图库索引状态
            self.check_library_index_status()

            # 加载清洗类型标记并设置UI
            clean_type = self.load_clean_type_marker(folder)
            self.set_clean_type_from_marker(clean_type)

            # 保存图库路径配置
            self.save_library_path_config(folder)

    def update_drag_drop_area_text(self):
        """更新拖拽区域的文本"""
        if hasattr(self, 'drag_drop_area'):
            library_folder = self.folder_path_edit.text()
            if library_folder:
                self.drag_drop_area.setText(f"""
🖼️ 拖拽图片到此区域

图库文件夹: {library_folder}
支持单个或多个图片文件
自动执行：名字清洗 + 横向放置 + 复制到图库文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
                """)
            else:
                self.drag_drop_area.setText("""
🖼️ 拖拽图片到此区域

请先选择图库文件夹
支持单个或多个图片文件
自动执行：名字清洗 + 横向放置 + 复制到图库文件夹

支持格式：JPG, PNG, BMP, GIF, TIFF, WEBP
                """)

    def check_library_index_status(self):
        """检查图库索引状态"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            self.index_status_label.setText("索引状态: 不可用")
            self.index_count_label.setText("图片数量: --")
            return

        # 启动索引状态检查工作线程
        if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
            return  # 如果已有索引任务在运行，不重复检查

        self.index_worker = IndexLibraryWorker(library_folder, 'check_status')
        self.index_worker.index_status.connect(self.update_index_status)
        self.index_worker.finished.connect(self.index_check_finished)
        self.index_worker.start()

    def update_index_status(self, is_indexed: bool, count: int, status_text: str):
        """更新索引状态显示"""
        if is_indexed:
            self.index_status_label.setText(f"索引状态: ✅ 已建立")
            self.index_status_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #4CAF50;
                    padding: 5px;
                    border: 1px solid #4CAF50;
                    border-radius: 3px;
                    background-color: #e8f5e8;
                }
            """)
        else:
            self.index_status_label.setText(f"索引状态: ❌ 未建立")
            self.index_status_label.setStyleSheet("""
                QLabel {
                    font-size: 12px;
                    color: #f44336;
                    padding: 5px;
                    border: 1px solid #f44336;
                    border-radius: 3px;
                    background-color: #ffebee;
                }
            """)

        self.index_count_label.setText(f"图片数量: {count}")

    def index_check_finished(self, success: bool, message: str, count: int):
        """索引状态检查完成"""
        if not success:
            self.update_log(f"索引状态检查: {message}")

    def save_library_path_config(self, library_path):
        """保存图库路径到配置文件"""
        try:
            # 创建配置目录（如果不存在）
            config_dir = os.path.join(os.path.expanduser('~'), '.dachuan-image-tool')
            os.makedirs(config_dir, exist_ok=True)

            # 保存配置
            config = {
                'library_path': library_path,
                'last_updated': time.time()
            }

            config_file = os.path.join(config_dir, 'config.json')
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.update_log(f"图库路径已保存到配置文件")
        except Exception as e:
            self.update_log(f"保存图库路径配置失败: {str(e)}")

    def load_library_path_config(self):
        """从配置文件加载图库路径"""
        try:
            config_file = os.path.join(os.path.expanduser('~'), '.dachuan-image-tool', 'config.json')
            if not os.path.exists(config_file):
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            library_path = config.get('library_path', '')
            if library_path and os.path.exists(library_path):
                self.folder_path_edit.setText(library_path)
                self.update_log(f"已恢复图库文件夹路径: {library_path}")

                # 启用功能按钮
                self.rotate_images_button.setEnabled(True)
                self.move_and_clean_button.setEnabled(True)
                self.index_library_button.setEnabled(True)

                # 更新拖拽区域的提示文本
                self.update_drag_drop_area_text()

                # 加载清洗类型标记并设置UI
                clean_type = self.load_clean_type_marker(library_path)
                self.set_clean_type_from_marker(clean_type)

                # 检查图库索引状态
                self.check_library_index_status()
            else:
                if library_path:
                    self.update_log(f"保存的图库路径不存在，已清除: {library_path}")
        except Exception as e:
            self.update_log(f"加载图库路径配置失败: {str(e)}")

    def save_clean_type_marker(self, library_path, clean_type):
        """保存清洗类型标记到图库.index隐藏文件夹"""
        try:
            if not library_path or not clean_type:
                return

            # 创建.index隐藏文件夹
            index_dir = os.path.join(library_path, '.index')
            os.makedirs(index_dir, exist_ok=True)

            # 保存清洗类型标记文件
            marker_file = os.path.join(index_dir, 'clean_type_marker.txt')
            with open(marker_file, 'w', encoding='utf-8') as f:
                f.write(clean_type)

            self.update_log(f"已保存清洗类型标记: {clean_type}")
        except Exception as e:
            self.update_log(f"保存清洗类型标记失败: {str(e)}")

    def load_clean_type_marker(self, library_path):
        """从图库.index隐藏文件夹加载清洗类型标记"""
        try:
            if not library_path:
                return '数字'  # 默认返回数字类型

            # 检查标记文件是否存在
            marker_file = os.path.join(library_path, '.index', 'clean_type_marker.txt')
            if not os.path.exists(marker_file):
                return '数字'  # 默认返回数字类型

            # 读取标记文件
            with open(marker_file, 'r', encoding='utf-8') as f:
                clean_type = f.read().strip()

            # 验证清洗类型是否有效
            if clean_type in ['数字', '字母', '中文']:
                self.update_log(f"已加载清洗类型标记: {clean_type}")
                return clean_type
            else:
                self.update_log(f"无效的清洗类型标记: {clean_type}，使用默认数字类型")
                return '数字'

        except Exception as e:
            self.update_log(f"加载清洗类型标记失败: {str(e)}，使用默认数字类型")
            return '数字'

    def set_clean_type_from_marker(self, clean_type):
        """根据标记设置清洗类型单选按钮"""
        try:
            if clean_type == '数字':
                self.digital_radio.setChecked(True)
            elif clean_type == '字母':
                self.letter_radio.setChecked(True)
            elif clean_type == '中文':
                self.chinese_radio.setChecked(True)
            else:
                self.digital_radio.setChecked(True)  # 默认选择数字类型

            # 更新示例显示
            self.update_clean_type_example()
        except Exception as e:
            self.update_log(f"设置清洗类型失败: {str(e)}")

    def on_clean_type_changed(self):
        """清洗类型改变时的处理函数"""
        try:
            library_path = self.folder_path_edit.text()
            if library_path:
                clean_type = self.get_selected_clean_type()
                self.save_clean_type_marker(library_path, clean_type)
        except Exception as e:
            self.update_log(f"处理清洗类型改变失败: {str(e)}")

    def update_clean_type_example(self):
        """更新清洗类型示例显示"""
        if hasattr(self, 'example_label'):
            if self.digital_radio.isChecked():
                self.example_label.setText("示例：O0027水蜜桃.jpg → O0027.jpg")
            elif self.letter_radio.isChecked():
                self.example_label.setText("示例：Photo123中文.jpg → Photo123.jpg")
            elif self.chinese_radio.isChecked():
                self.example_label.setText("示例：【粉】卷毛小狗梦幻星123.jpg → 【粉】卷毛小狗梦幻星.jpg")

    def get_selected_clean_type(self):
        """获取当前选择的清洗类型"""
        if hasattr(self, 'digital_radio') and self.digital_radio.isChecked():
            return '数字'
        elif hasattr(self, 'letter_radio') and self.letter_radio.isChecked():
            return '字母'
        elif hasattr(self, 'chinese_radio') and self.chinese_radio.isChecked():
            return '中文'
        else:
            return '数字'  # 默认返回数字类型

    def manage_library_index(self):
        """管理图库索引"""
        library_folder = self.folder_path_edit.text()
        if not library_folder:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        if not HAS_IMAGE_INDEXER:
            QMessageBox.warning(self, "错误", "图库索引器模块未找到，无法执行索引操作")
            return

        # 显示索引管理对话框
        self.show_index_management_dialog()

    def show_index_management_dialog(self):
        """显示索引管理对话框"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QCheckBox

        dialog = QDialog(self)
        dialog.setWindowTitle("图库索引管理")
        dialog.setFixedSize(400, 300)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("图库索引管理")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 当前状态显示
        status_label = QLabel(f"当前图库: {self.folder_path_edit.text()}")
        status_label.setWordWrap(True)
        layout.addWidget(status_label)

        current_status = QLabel()
        current_status.setText(f"索引状态: {self.index_status_label.text().replace('索引状态: ', '')}")
        layout.addWidget(current_status)

        current_count = QLabel()
        current_count.setText(f"图片数量: {self.index_count_label.text().replace('图片数量: ', '')}")
        layout.addWidget(current_count)

        # 快速模式选项
        fast_mode_checkbox = QCheckBox("快速模式（获取图片尺寸，跳过详细EXIF数据，速度更快）")
        fast_mode_checkbox.setChecked(True)  # 默认使用快速模式
        layout.addWidget(fast_mode_checkbox)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 建立/重建索引按钮
        build_button = QPushButton("建立/重建索引")
        build_button.clicked.connect(lambda: self.start_index_operation('build_index', fast_mode_checkbox.isChecked(), dialog))

        # 更新索引按钮
        update_button = QPushButton("更新索引")
        update_button.clicked.connect(lambda: self.start_index_operation('update_index', fast_mode_checkbox.isChecked(), dialog))

        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(build_button)
        button_layout.addWidget(update_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        dialog.exec()

    def start_index_operation(self, operation_type: str, fast_mode: bool, dialog):
        """启动索引操作"""
        library_folder = self.folder_path_edit.text()

        # 关闭对话框
        dialog.accept()

        # 检查是否有其他任务在运行
        if (hasattr(self, 'worker') and self.worker and self.worker.isRunning()) or \
           (hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning()) or \
           (hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning()):
            QMessageBox.warning(self, "错误", "有其他处理任务正在运行，请稍后再试")
            return

        # 启动索引操作
        operation_names = {
            'build_index': '建立索引',
            'update_index': '更新索引'
        }

        operation_name = operation_names.get(operation_type, operation_type)
        mode_text = "快速模式" if fast_mode else "完整模式"

        self.update_log(f"开始{operation_name}（{mode_text}）...")

        # 禁用相关按钮
        self.index_library_button.setEnabled(False)
        self.move_and_clean_button.setEnabled(False)
        self.rotate_images_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 创建并启动索引工作线程
        self.index_worker = IndexLibraryWorker(library_folder, operation_type, fast_mode)

        # 连接信号槽
        self.index_worker.progress.connect(self.update_progress)
        self.index_worker.log.connect(self.update_log)
        self.index_worker.status.connect(self.update_log)
        self.index_worker.index_status.connect(self.update_index_status)
        self.index_worker.finished.connect(self.index_operation_finished)

        # 启动工作线程
        self.index_worker.start()

    def index_operation_finished(self, success: bool, message: str, indexed_count: int):
        """索引操作完成"""
        if success:
            self.statusBar().showMessage('索引操作完成')
            QMessageBox.information(self, '完成', message)
        else:
            self.statusBar().showMessage('索引操作失败')
            QMessageBox.warning(self, '错误', message)

        # 重置UI状态
        self.reset_ui_state()

        # 重新检查索引状态
        self.check_library_index_status()

    def handle_dropped_files(self, image_files: List[str]):
        """处理拖拽的图片文件"""
        # 检查是否选择了图库文件夹
        library_folder = self.folder_path_edit.text()
        if not library_folder:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 规范化路径格式，使用反斜杠
        library_folder = library_folder.replace('/', '\\')

        if not os.path.exists(library_folder):
            QMessageBox.warning(self, "错误", f"图库文件夹不存在: {library_folder}")
            return

        # 检查是否有图片文件
        if not image_files:
            QMessageBox.warning(self, "错误", "没有检测到有效的图片文件")
            return

        # 规范化图片文件路径
        normalized_image_files = []
        for image_file in image_files:
            normalized_path = image_file.replace('/', '\\')
            if os.path.exists(normalized_path):
                normalized_image_files.append(normalized_path)
            else:
                self.update_log(f"警告：图片文件不存在，跳过: {image_file}")

        if not normalized_image_files:
            QMessageBox.warning(self, "错误", "没有找到有效的图片文件")
            return

        # 检查是否有其他处理任务正在运行
        if (hasattr(self, 'worker') and self.worker and self.worker.isRunning()) or \
           (hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning()) or \
           (hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning()):
            QMessageBox.warning(self, "错误", "有其他处理任务正在运行，请稍后再试")
            return

        # 开始处理拖拽的文件
        self.start_drag_drop_processing(normalized_image_files, library_folder)

    def start_drag_drop_processing(self, image_files: List[str], library_folder: str):
        """开始拖拽处理"""
        self.update_log(f"开始处理拖拽的 {len(image_files)} 个图片文件到图库文件夹...")

        # 禁用按钮，防止重复操作
        self.rotate_images_button.setEnabled(False)
        self.move_and_clean_button.setEnabled(False)
        self.index_library_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 默认使用跳过策略（UI已隐藏）
        duplicate_strategy = "skip"  # 固定使用跳过策略
        self.update_log(f"同名同宽高比图片策略: 跳过")

        # 获取当前选择的清洗类型
        clean_type = self.get_selected_clean_type()
        self.update_log(f"清洗类型: {clean_type}")

        # 创建拖拽处理工作线程（启用重复检测，传递策略和清洗类型）
        self.drag_drop_worker = DragDropProcessorWorker(
            image_files,
            library_folder,
            check_duplicates=True,
            duplicate_strategy=duplicate_strategy,
            clean_type=clean_type
        )

        # 连接信号槽
        self.drag_drop_worker.progress.connect(self.update_progress)
        self.drag_drop_worker.log.connect(self.update_log)
        self.drag_drop_worker.finished.connect(self.drag_drop_process_finished)
        self.drag_drop_worker.duplicate_found.connect(self.handle_duplicate_found)

        # 启动工作线程
        self.drag_drop_worker.start()

    def handle_duplicate_found(self, image_path: str, existing_path: str, clean_filename: str):
        """处理发现重复文件的情况"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

        # 创建重复文件处理对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("发现重复文件")
        dialog.setFixedSize(500, 300)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("发现重复文件")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 文件信息
        info_label = QLabel(f"""
要添加的文件: {os.path.basename(image_path)}
清洗后文件名: {clean_filename}
已存在文件: {existing_path}

请选择处理方式：
        """)
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 跳过按钮
        skip_button = QPushButton("跳过此文件")
        skip_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "skip"))

        # 替换按钮
        replace_button = QPushButton("替换现有文件")
        replace_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "replace"))

        # 取消按钮
        cancel_button = QPushButton("取消所有操作")
        cancel_button.clicked.connect(lambda: self.set_duplicate_action_and_close(dialog, image_path, "cancel"))

        button_layout.addWidget(skip_button)
        button_layout.addWidget(replace_button)
        button_layout.addWidget(cancel_button)

        layout.addLayout(button_layout)

        # 显示对话框（非阻塞）
        dialog.show()

    def set_duplicate_action_and_close(self, dialog, image_path: str, action: str):
        """设置重复文件处理动作并关闭对话框"""
        if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker:
            if action == "cancel":
                # 取消所有操作
                self.drag_drop_worker.terminate()
                self.reset_ui_state()
                self.update_log("用户取消了拖拽操作")
            else:
                # 设置处理动作
                self.drag_drop_worker.set_duplicate_action(image_path, action)
                action_text = "跳过" if action == "skip" else "替换"
                self.update_log(f"用户选择{action_text}重复文件: {os.path.basename(image_path)}")

        dialog.close()

    def show_drag_drop_statistics(self, statistics: dict, file_details: dict = None):
        """显示拖拽处理统计信息弹窗"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                                     QLabel, QTextEdit, QTabWidget, QWidget, QScrollArea)

        # 创建统计信息对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("拖拽处理完成")
        dialog.setFixedSize(700, 600)
        dialog.setModal(True)

        layout = QVBoxLayout(dialog)

        # 标题
        title_label = QLabel("🎉 拖拽处理完成！")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #4CAF50;")
        layout.addWidget(title_label)

        # 创建标签页组件
        tab_widget = QTabWidget()

        # 第一个标签页：总体统计
        summary_widget = QWidget()
        summary_layout = QVBoxLayout(summary_widget)

        # 统计信息文本
        stats_text = f"""
📊 处理统计：
• 总文件数：{statistics.get('total_files', 0)}
• 处理成功：{statistics.get('success_files', 0)}
• 跳过文件：{statistics.get('skipped_files', 0)}
• 处理失败：{statistics.get('error_files', 0)}

🔄 文件处理详情：
• 新文件处理：{statistics.get('new_files_processed', 0)}
• 同名同宽高比覆盖：{statistics.get('same_aspect_ratio_overwritten', 0)}
• 同名不同宽高比重命名：{statistics.get('different_aspect_ratio_renamed', 0)}
        """

        # 如果有特殊情况，添加说明
        if statistics.get('new_files_processed', 0) > 0:
            stats_text += f"\n💡 说明：{statistics.get('new_files_processed', 0)} 个新图片已成功添加到图库"

        if statistics.get('same_aspect_ratio_overwritten', 0) > 0:
            stats_text += f"\n💡 说明：{statistics.get('same_aspect_ratio_overwritten', 0)} 个同名同宽高比图片已按策略执行覆盖操作"

        if statistics.get('different_aspect_ratio_renamed', 0) > 0:
            stats_text += f"\n📝 说明：{statistics.get('different_aspect_ratio_renamed', 0)} 个同名不同宽高比图片已按宽高比倒序分配序号"

        stats_label = QLabel(stats_text)
        stats_label.setWordWrap(True)
        stats_label.setStyleSheet("font-size: 12px; color: #333; background-color: #f9f9f9; padding: 15px; border-radius: 5px;")
        summary_layout.addWidget(stats_label)

        tab_widget.addTab(summary_widget, "📊 总体统计")

        # 如果有详细文件信息，添加详细信息标签页
        if file_details:
            self._add_file_details_tabs(tab_widget, file_details)

        layout.addWidget(tab_widget)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        ok_button = QPushButton("确定")
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        ok_button.clicked.connect(dialog.accept)

        button_layout.addWidget(ok_button)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 显示对话框
        dialog.exec()

    def _add_file_details_tabs(self, tab_widget, file_details: dict):
        """添加详细文件信息标签页"""
        from PyQt6.QtWidgets import QTextEdit, QWidget, QVBoxLayout

        # 跳过的文件标签页
        if file_details.get('skipped_files'):
            skipped_widget = QWidget()
            skipped_layout = QVBoxLayout(skipped_widget)

            skipped_text = QTextEdit()
            skipped_text.setReadOnly(True)

            content = f"⏭️ 跳过的文件 ({len(file_details['skipped_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['skipped_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            skipped_text.setPlainText(content)
            skipped_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            skipped_layout.addWidget(skipped_text)

            tab_widget.addTab(skipped_widget, f"⏭️ 跳过 ({len(file_details['skipped_files'])})")

        # 覆盖的文件标签页
        if file_details.get('overwritten_files'):
            overwritten_widget = QWidget()
            overwritten_layout = QVBoxLayout(overwritten_widget)

            overwritten_text = QTextEdit()
            overwritten_text.setReadOnly(True)

            content = f"🔄 覆盖的文件 ({len(file_details['overwritten_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['overwritten_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                if file_info['original_name'] != file_info['target_name']:
                    content += f"   目标文件：{file_info['target_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            overwritten_text.setPlainText(content)
            overwritten_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            overwritten_layout.addWidget(overwritten_text)

            tab_widget.addTab(overwritten_widget, f"🔄 覆盖 ({len(file_details['overwritten_files'])})")

        # 新文件标签页
        if file_details.get('new_files'):
            new_files_widget = QWidget()
            new_files_layout = QVBoxLayout(new_files_widget)

            new_files_text = QTextEdit()
            new_files_text.setReadOnly(True)

            content = f"✨ 新添加的文件 ({len(file_details['new_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['new_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                if file_info['original_name'] != file_info['target_name']:
                    content += f"   目标文件：{file_info['target_name']}\n"
                content += "\n"

            new_files_text.setPlainText(content)
            new_files_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            new_files_layout.addWidget(new_files_text)

            tab_widget.addTab(new_files_widget, f"✨ 新文件 ({len(file_details['new_files'])})")

        # 重命名的文件标签页
        if file_details.get('renamed_files'):
            renamed_widget = QWidget()
            renamed_layout = QVBoxLayout(renamed_widget)

            renamed_text = QTextEdit()
            renamed_text.setReadOnly(True)

            content = f"📝 重命名的文件 ({len(file_details['renamed_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['renamed_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   重命名为：{file_info['target_name']}\n"
                content += f"   原因：{file_info['reason']}\n\n"

            renamed_text.setPlainText(content)
            renamed_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px;")
            renamed_layout.addWidget(renamed_text)

            tab_widget.addTab(renamed_widget, f"📝 重命名 ({len(file_details['renamed_files'])})")

        # 错误文件标签页
        if file_details.get('error_files'):
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)

            error_text = QTextEdit()
            error_text.setReadOnly(True)

            content = f"❌ 处理失败的文件 ({len(file_details['error_files'])} 个)：\n\n"
            for i, file_info in enumerate(file_details['error_files'], 1):
                content += f"{i}. {file_info['original_name']}\n"
                content += f"   错误：{file_info['error']}\n\n"

            error_text.setPlainText(content)
            error_text.setStyleSheet("font-family: 'Consolas', 'Monaco', monospace; font-size: 11px; color: #d32f2f;")
            error_layout.addWidget(error_text)

            tab_widget.addTab(error_widget, f"❌ 错误 ({len(file_details['error_files'])})")

    def drag_drop_process_finished(self, success: bool, message: str, processed_count: int, statistics: dict = None, file_details: dict = None):
        """拖拽处理完成"""
        try:
            if success:
                self.statusBar().showMessage('拖拽处理完成')

                # 显示详细统计信息弹窗
                if statistics:
                    self.show_drag_drop_statistics(statistics, file_details)
                else:
                    # 兼容旧版本，显示简单信息
                    detailed_message = f"{message}\n处理了 {processed_count} 个文件"
                    QMessageBox.information(self, '完成', detailed_message)

                # 如果处理成功且有图库索引器，立即更新索引确保新图片能被正确显示
                if processed_count > 0 and HAS_IMAGE_INDEXER:
                    self.update_log("🔄 正在重建图库索引以确保新图片正确显示...")
                    try:
                        # 使用同步方式完整重建索引，确保第一次拖拽后立即显示正确
                        library_folder = self.folder_path_edit.text()
                        if library_folder:
                            indexer = ImageIndexerDuckDB(fast_mode=True)
                            if indexer.is_indexed(library_folder):
                                success, message, count = indexer.scan_library(library_folder, fast_mode=True)
                                if success:
                                    self.update_log(f"✅ 索引重建完成，共索引 {count} 个文件")
                                    # 更新索引状态显示
                                    self.update_index_status(True, count, f"已索引 {count} 个文件")
                                    
                                    # 强制刷新UI，确保显示正确
                                    from PyQt6.QtWidgets import QApplication
                                    QApplication.processEvents()
                                    
                                    # 延迟再次检查索引状态，确保显示正确
                                    from PyQt6.QtCore import QTimer
                                    QTimer.singleShot(500, self.check_library_index_status)
                                else:
                                    self.update_log(f"❌ 索引重建失败: {message}")
                            else:
                                self.update_log("图库尚未建立索引，跳过索引更新")
                    except Exception as e:
                        self.update_log(f"❌ 索引更新失败: {str(e)}")
            else:
                self.statusBar().showMessage('拖拽处理失败')
                QMessageBox.warning(self, '错误', message)

        except Exception as e:
            self.update_log(f"拖拽处理完成回调出错: {str(e)}")
        finally:
            # 重置UI状态
            self.reset_ui_state()

    def update_library_index_after_drag_drop_async(self):
        """异步更新图库索引，避免阻塞UI"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            return

        # 检查是否已有索引任务在运行
        if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
            self.update_log("索引任务正在运行中，跳过本次更新")
            return

        # 使用强制完整重建索引，确保第一次拖拽后也能正确显示
        self.update_log("正在进行完整索引重建以确保显示正确...")
        try:
            # 创建索引工作线程进行完整重建
            self.index_worker = IndexLibraryWorker(library_folder, 'build_index', fast_mode=True)
            self.index_worker.index_status.connect(self.update_index_status)
            self.index_worker.finished.connect(self.drag_drop_index_update_finished)
            self.index_worker.start()
        except Exception as e:
            self.update_log(f"索引重建失败: {str(e)}")

    def drag_drop_index_update_finished(self, success: bool, message: str, count: int):
        """拖拽后索引更新完成回调"""
        if success:
            self.update_log(f"✅ 拖拽后索引更新完成，共索引 {count} 个文件")
            # 强制刷新UI显示，确保第一次拖拽后也能正确显示
            QApplication.processEvents()
            # 再次检查索引状态，确保显示正确
            self.check_library_index_status()
        else:
            self.update_log(f"❌ 拖拽后索引更新失败: {message}")

    def update_library_index_after_drag_drop(self):
        """拖拽处理完成后更新图库索引（同步版本，保留兼容性）"""
        library_folder = self.folder_path_edit.text()
        if not library_folder or not HAS_IMAGE_INDEXER:
            return

        try:
            # 规范化路径格式，使用反斜杠
            library_folder = library_folder.replace('/', '\\')

            # 创建新的索引器实例，避免连接冲突
            indexer = ImageIndexerDuckDB(fast_mode=True)

            # 检查是否已有索引
            if indexer.is_indexed(library_folder):
                # 使用完整重建而不是增量更新，确保第一次拖拽后显示正确
                self.update_log("正在进行完整索引重建以确保显示正确...")
                success, message, count = indexer.scan_library(library_folder, fast_mode=True)

                if success:
                    self.update_log(f"索引重建完成，共索引 {count} 个文件")
                    # 更新索引状态显示
                    self.update_index_status(True, count, f"已索引 {count} 个文件")
                else:
                    self.update_log(f"索引重建失败: {message}")
            else:
                self.update_log("图库尚未建立索引，跳过索引更新")

        except Exception as e:
            self.update_log(f"更新索引时出错: {str(e)}")
            import traceback
            self.update_log(f"详细错误信息: {traceback.format_exc()}")

    def _incremental_index_update(self, indexer, library_folder):
        """执行真正的增量索引更新，只处理新增的图片文件"""
        try:
            # 设置图库路径
            indexer.set_library_path(library_folder)

            # 收集当前文件夹中的所有图片文件
            current_files = set()
            supported_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp')
            for root, dirs, files in os.walk(library_folder):
                for file in files:
                    if file.lower().endswith(supported_extensions):
                        rel_path = os.path.relpath(os.path.join(root, file), library_folder)
                        # 规范化路径，使用反斜杠
                        rel_path = rel_path.replace('/', '\\')
                        current_files.add(rel_path)

            # 获取数据库中已索引的文件
            indexed_files = set()
            try:
                if indexer.db:
                    result = indexer.db.execute("SELECT relative_path FROM image_files").fetchall()
                    indexed_files = {row[0] for row in result}
            except Exception as e:
                self.update_log(f"获取已索引文件列表失败: {str(e)}")
                # 如果无法获取已索引文件，说明可能是第一次建立索引
                if not indexed_files:
                    self.update_log("数据库为空，执行完整索引建立...")
                    return indexer.scan_library(library_folder, fast_mode=True)
                else:
                    return False, f"获取已索引文件列表失败: {str(e)}", 0

            # 找出新增的文件
            new_files = current_files - indexed_files

            if not new_files:
                self.update_log("没有发现新增的图片文件")
                return True, "索引已是最新", len(indexed_files)

            self.update_log(f"发现 {len(new_files)} 个新增图片文件，开始增量索引...")

            # 使用完整重建而不是增量更新，确保第一次拖拽后显示正确
            self.update_log("为确保显示正确，执行完整索引重建...")
            return indexer.scan_library(library_folder, fast_mode=True)

        except Exception as e:
            self.update_log(f"❌ 增量索引更新失败: {str(e)}")
            # 如果增量更新失败，回退到完整重建
            self.update_log("增量更新失败，回退到完整重建...")
            try:
                return indexer.scan_library(library_folder, fast_mode=True)
            except Exception as rebuild_error:
                return False, f"完整重建也失败: {str(rebuild_error)}", 0

    def update_log(self, message):
        """更新日志"""
        self.log_text.append(message)
        # 滚动到底部以显示最新日志
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        # 强制立即更新UI
        QApplication.processEvents()

    def clean_image_names(self):
        """执行文件名清洗"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        self.update_log("开始执行图库文件名清洗...")
        self.start_processing('clean_names')

    def rotate_images(self):
        """执行图片旋转"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        self.update_log("开始执行图库图片横向放置...")
        self.start_processing('rotate_images')

    def move_images(self):
        """执行图片移动整理"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认操作",
            "此操作将：\n"
            "1. 将所有子文件夹中的图片移动到主目录\n"
            "2. 同名文件将被覆盖\n"
            "3. 删除所有空的子文件夹\n\n"
            "此操作不可撤销，是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.update_log("开始执行图库图片移动整理...")
            self.start_processing('move_images')

    def move_and_clean_images(self):
        """执行图片移动整理并清洗文件名（合并功能）"""
        folder_path = self.folder_path_edit.text()
        if not folder_path:
            QMessageBox.warning(self, "错误", "请先选择图库文件夹")
            return

        # 确认对话框
        duplicate_numbering_status = "启用" if self.duplicate_numbering_checkbox.isChecked() else "禁用"
        reply = QMessageBox.question(
            self,
            "确认操作",
            f"此操作将：\n"
            f"1. 将所有子文件夹中的图片移动到主目录\n"
            f"2. 对每张移动的图片进行文件名清洗\n"
            f"3. 同名图片序号功能：{duplicate_numbering_status}\n"
            f"4. 删除所有空的子文件夹\n\n"
            f"此操作不可撤销，是否继续？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.update_log("开始执行图库图片移动整理并清洗文件名...")
            self.start_processing('move_and_clean')

    def start_processing(self, operation_type):
        """开始处理"""
        folder_path = self.folder_path_edit.text()

        # 禁用功能按钮，启用停止按钮
        self.rotate_images_button.setEnabled(False)
        self.move_and_clean_button.setEnabled(False)
        self.stop_button.setEnabled(True)

        # 获取清洗设置
        enable_duplicate_numbering = self.duplicate_numbering_checkbox.isChecked()
        clean_type = self.get_selected_clean_type()

        # 创建Worker线程处理，传递同名图片序号开关状态和清洗类型
        self.worker = ImageProcessorWorker(folder_path, operation_type, enable_duplicate_numbering, clean_type)

        # 连接信号槽
        self.worker.progress.connect(self.update_progress)
        self.worker.log.connect(self.update_log)
        self.worker.finished.connect(self.process_finished)

        # 启动Worker线程
        self.worker.start()

    def stop_processing(self):
        """停止处理"""
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            self.update_log("正在请求停止处理...")
            self.worker.request_stop()
            self.stop_button.setEnabled(False)
            self.stop_button.setText("正在停止...")

        if hasattr(self, 'drag_drop_worker') and self.drag_drop_worker and self.drag_drop_worker.isRunning():
            self.update_log("正在停止拖拽处理...")
            self.drag_drop_worker.terminate()
            self.drag_drop_worker.wait(3000)  # 等待最多3秒
            self.reset_ui_state()

        if hasattr(self, 'index_worker') and self.index_worker and self.index_worker.isRunning():
            self.update_log("正在停止索引操作...")
            self.index_worker.request_stop()
            self.stop_button.setEnabled(False)
            self.stop_button.setText("正在停止...")

    def reset_ui_state(self):
        """重置UI状态"""
        # 重新启用功能按钮，禁用停止按钮
        if self.folder_path_edit.text():
            self.rotate_images_button.setEnabled(True)
            self.move_and_clean_button.setEnabled(True)
            self.index_library_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.stop_button.setText("停止处理")

    def process_finished(self, success, message):
        """处理完成"""
        if success:
            self.statusBar().showMessage('处理完成')
            QMessageBox.information(self, '完成', message)
        else:
            self.statusBar().showMessage('处理失败')
            QMessageBox.warning(self, '错误', message)

        # 重置UI状态
        self.reset_ui_state()


if __name__ == '__main__':
    app = QApplication(sys.argv)

    # 初始化SupabaseHelper
    supabase_helper = SupabaseHelper()

    # 显示登录对话框
    login_dialog = LoginDialog(supabase_helper, ROBOT_SMART_NAME, ROBOT_CURRENT_VERSION)
    if login_dialog.exec() == 1:  # 1 表示 QDialog.Accepted
        # 登录成功，创建主界面
        window = ImageProcessorApp()

        # 设置SupabaseHelper引用
        window.supabase_helper = supabase_helper

        # 检查版本（必须在设置supabase_helper之后）
        window.check_version()

        # 在主界面日志区域显示登录成功信息
        if supabase_helper.is_connected():
            try:
                email = supabase_helper.get_user_email()
                if email:
                    window.update_log(f"用户 {email} 登录成功")
                    window.update_log("正在初始化应用...")
                else:
                    window.update_log("用户登录成功")
            except Exception as e:
                window.update_log(f"获取用户信息时出错: {e}")

        # 显示主界面
        window.show()
        sys.exit(app.exec())
    else:
        # 用户取消登录，退出程序
        sys.exit(0)
