# 清洗类型标记功能实现说明

## 功能概述

根据用户需求，实现了以下功能：

1. **选择清洗类型时自动标记**：当用户选择数字、字母、中文类型时，在图库.index隐藏文件夹下保存所选择的类型标记
2. **程序启动时自动检测标记**：程序启动时检测是否有清洗类型的标记，如有标记则自动切换到对应的标记类型
3. **删除自动检测功能**：完全删除了自动检测UI及相关代码逻辑

## 实现细节

### 1. 删除自动检测功能

- **删除UI组件**：移除了`auto_radio`自动检测单选按钮
- **删除核心函数**：删除了`detect_filename_type`函数
- **简化清洗逻辑**：修改`clean_filename`函数，移除'auto'处理逻辑
- **更新UI处理**：修改示例显示和类型获取函数，移除自动检测相关代码
- **清理统计逻辑**：删除文件处理过程中的自动检测统计功能

### 2. 实现清洗类型标记功能

#### 标记文件管理
- **保存标记**：`save_clean_type_marker(library_path, clean_type)`
  - 在`{图库路径}/.index/`目录下创建`clean_type_marker.txt`文件
  - 文件内容为清洗类型：'数字'、'字母'、'中文'
  - 自动创建.index隐藏文件夹（Windows系统中以点开头的文件夹默认隐藏）

- **读取标记**：`load_clean_type_marker(library_path)`
  - 从标记文件读取清洗类型
  - 验证类型有效性，无效时默认返回'数字'
  - 文件不存在时默认返回'数字'

- **设置UI状态**：`set_clean_type_from_marker(clean_type)`
  - 根据读取的标记设置对应的单选按钮
  - 更新示例显示

#### 信号连接和触发时机
- **保存时机**：连接清洗类型按钮组的`buttonClicked`信号到`on_clean_type_changed`函数
- **读取时机**：
  - 选择图库文件夹后（`select_library_folder`函数中）
  - 程序启动恢复图库路径后（`load_library_path_config`函数中）

### 3. 文件结构

```
图库文件夹/
├── .index/                    # 隐藏文件夹
│   └── clean_type_marker.txt  # 清洗类型标记文件
├── 图片文件1.jpg
├── 图片文件2.png
└── ...
```

### 4. 标记文件格式

- **文件名**：`clean_type_marker.txt`
- **文件位置**：`{图库路径}/.index/clean_type_marker.txt`
- **文件内容**：纯文本，内容为以下之一：
  - `数字`
  - `字母`
  - `中文`

## 错误处理

1. **文件操作异常**：保存和读取标记文件时的IO异常处理
2. **无效标记内容**：读取到无效类型时自动回退到默认数字类型
3. **文件不存在**：标记文件不存在时使用默认数字类型
4. **路径异常**：图库路径为空或无效时的处理

## 向后兼容性

- 标记文件不存在时默认使用数字类型，保持原有行为
- 现有图库文件夹不受影响，首次使用时会创建.index文件夹
- 删除自动检测功能不影响现有的三种清洗类型功能

## 测试验证

创建了完整的测试脚本`test_clean_type_marker.py`，验证了：
- 三种清洗类型的保存和读取
- 默认行为（无标记文件时）
- 无效标记内容的处理
- .index文件夹的正确创建
- 标记文件的正确格式

所有测试均通过，功能实现完整且稳定。

## 使用流程

1. **首次使用**：选择图库文件夹后，默认为数字类型
2. **切换类型**：点击字母或中文类型单选按钮，自动保存标记
3. **重启程序**：程序启动时自动读取标记并设置对应类型
4. **更换图库**：选择不同图库文件夹时，自动读取该图库的标记设置

这样实现了用户需求中的所有功能点，提供了便捷的清洗类型记忆功能。
